package com.tydic.nbchat.pay.api.bo.emus;

public enum PayBusiType {

    ALL("all", "所有支付"),
    TDH("tdh", "数字人和声音定制"),
    VIP("vip", "会员支付");

    private String code;
    private String name;

    public boolean equals(String code) {
        return this.code.equals(code);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private PayBusiType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (PayBusiType field : PayBusiType.values()) {
            if (field.code.equals(code)) {
                return field.name;
            }
        }
        return "";
    }
}
