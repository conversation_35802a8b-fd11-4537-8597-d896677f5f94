<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.user.mapper.NbchatUserVipMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.user.mapper.po.NbchatUserVip">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="vip_type" property="vipType" jdbcType="CHAR"/>
        <result column="vip_desc" property="vipDesc" jdbcType="VARCHAR"/>
        <result column="vip_status" property="vipStatus" jdbcType="CHAR"/>
        <result column="is_company" property="isCompany" jdbcType="CHAR"/>
        <result column="vip_start" property="vipStart" jdbcType="TIMESTAMP"/>
        <result column="vip_end" property="vipEnd" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_experienced" property="isExperienced" jdbcType="CHAR"/>
        <result column="vip_version" property="vipVersion" jdbcType="CHAR"/>

    </resultMap>
    <sql id="Base_Column_List">
        id, tenant_code, user_id, vip_type, vip_desc, vip_status, is_company, vip_start,
    vip_end, create_time, update_time,is_experienced,vip_version
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from nbchat_user_vip
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nbchat_user_vip
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserVip">
        insert into nbchat_user_vip (id, tenant_code, user_id,
                                     vip_type, vip_desc, vip_status,
                                     is_company, vip_start, vip_end,
                                     create_time, update_time)
        values (#{id,jdbcType=BIGINT}, #{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
                #{vipType,jdbcType=CHAR}, #{vipDesc,jdbcType=VARCHAR}, #{vipStatus,jdbcType=CHAR},
                #{isCompany,jdbcType=CHAR}, #{vipStart,jdbcType=TIMESTAMP}, #{vipEnd,jdbcType=TIMESTAMP},
                #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserVip">
        insert into nbchat_user_vip
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="vipDesc != null">
                vip_desc,
            </if>
            <if test="vipStatus != null">
                vip_status,
            </if>
            <if test="isCompany != null">
                is_company,
            </if>
            <if test="vipStart != null">
                vip_start,
            </if>
            <if test="vipEnd != null">
                vip_end,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isExperienced != null">
                is_experienced,
            </if>
            <if test="vipVersion != null">
                vip_version,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=CHAR},
            </if>
            <if test="vipDesc != null">
                #{vipDesc,jdbcType=VARCHAR},
            </if>
            <if test="vipStatus != null">
                #{vipStatus,jdbcType=CHAR},
            </if>
            <if test="isCompany != null">
                #{isCompany,jdbcType=CHAR},
            </if>
            <if test="vipStart != null">
                #{vipStart,jdbcType=TIMESTAMP},
            </if>
            <if test="vipEnd != null">
                #{vipEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isExperienced != null">
                #{isExperienced,jdbcType=CHAR},
            </if>
            <if test="vipVersion != null">
                #{vipVersion,jdbcType=CHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserVip">
        update nbchat_user_vip
        <set>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=CHAR},
            </if>
            <if test="vipDesc != null">
                vip_desc = #{vipDesc,jdbcType=VARCHAR},
            </if>
            <if test="vipStatus != null">
                vip_status = #{vipStatus,jdbcType=CHAR},
            </if>
            <if test="isCompany != null">
                is_company = #{isCompany,jdbcType=CHAR},
            </if>
            <if test="vipStart != null">
                vip_start = #{vipStart,jdbcType=TIMESTAMP},
            </if>
            <if test="vipEnd != null">
                vip_end = #{vipEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isExperienced != null">
                is_experienced = #{isExperienced,jdbcType=CHAR},
            </if>
            <if test="vipVersion != null">
                vip_version = #{vipVersion,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserVip">
        update nbchat_user_vip
        set tenant_code = #{tenantCode,jdbcType=VARCHAR},
            user_id     = #{userId,jdbcType=VARCHAR},
            vip_type    = #{vipType,jdbcType=CHAR},
            vip_desc    = #{vipDesc,jdbcType=VARCHAR},
            vip_status  = #{vipStatus,jdbcType=CHAR},
            is_company  = #{isCompany,jdbcType=CHAR},
            vip_start   = #{vipStart,jdbcType=TIMESTAMP},
            vip_end     = #{vipEnd,jdbcType=TIMESTAMP},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from nbchat_user_vip
        where user_id = #{userId,jdbcType=VARCHAR}
        and tenant_code = #{tenantCode,jdbcType=VARCHAR}
        order by FIELD(vip_status,'1','0'), vip_type desc
    </select>

    <select id="selectExpiredVip" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_user_vip where #{checkTime} >= vip_end and vip_status = '1'
    </select>
</mapper>