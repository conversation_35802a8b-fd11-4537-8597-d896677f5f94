package com.tydic.nbchat.pay.mapper;

import com.tydic.nbchat.pay.mapper.po.PayOpDataSelectCondition;
import com.tydic.nbchat.pay.mapper.po.PayOpDataSelectResult;
import com.tydic.nbchat.pay.mapper.po.PayPriceCountResult;
import com.tydic.nbchat.pay.mapper.po.PayTradeRecord;

import java.util.List;

public interface PayTradeRecordMapper {

    int insertSelective(PayTradeRecord record);

    PayTradeRecord selectByPrimaryKey(String tradeNo);

    PayTradeRecord selectByOrderNo(String orderNo);

    PayTradeRecord selectByRefundNo(String refundNo);

    int updateByTradeNo(PayTradeRecord record);

    int updateByOrderNoSelective(PayTradeRecord trade);

    /**
     * 统计支付金额
     *
     * @return
     */
    PayPriceCountResult countPayPrice();

    List<PayOpDataSelectResult> selectPayTradeRecord(PayOpDataSelectCondition cond);

    /**
     * 统计数字人定制和声音定制支付订单
     *
     * @param payOpData
     * @return
     */
    List<PayOpDataSelectResult> selectPayTradeRecordByTdh(PayOpDataSelectCondition payOpData);

    /**
     * 统计会员支付订单
     *
     * @param payOpData
     * @return
     */
    List<PayOpDataSelectResult> selectPayTradeRecordByVip(PayOpDataSelectCondition payOpData);
}
