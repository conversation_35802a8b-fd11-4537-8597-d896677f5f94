package com.tydic.nbchat.pay.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PayOpDataSelectCondition implements Serializable {
    /**
     * 用户手机号
     */
    private String phone;
    private String busiType;
    /**
     * 商品skuId
     */
    private String skuId;
    /**
     * 支付开始时间
     */
    private Date payStartTime;
    /**
     * 支付结束时间
     */
    private Date payEndTime;
    /**
     * 目标租户
     */
    private String targetTenant;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 目标用户id
     */
    private String targetUserId;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 支付类型
     * 0-个人支付
     * 1-企业支付
     */
    private String paymentType;
    /**
     * 支付属性
     * 0 - 首次-新注册
     * 1 - 首次-历史注册
     * 2 - 老客-附购
     * 3 - 老客-升级
     * 4 - 老客-续费
     * 5 - 老客-回流
     */
    private String paymentAttr;
}
