<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.pay.mapper.PayTradeRecordMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.pay.mapper.po.PayTradeRecord">
        <id column="trade_no" property="tradeNo" jdbcType="VARCHAR"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="pay_no" property="payNo" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="pay_price" property="payPrice" jdbcType="INTEGER"/>
        <result column="pay_status" property="payStatus" jdbcType="VARCHAR"/>
        <result column="pay_desc" property="payDesc" jdbcType="VARCHAR"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="pay_type" property="payType" jdbcType="CHAR"/>
        <result column="busi_type" property="busiType" jdbcType="CHAR"/>
        <result column="refund_no" property="refundNo" jdbcType="CHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.tydic.nbchat.pay.mapper.po.PayTradeRecord" extends="BaseResultMap">
        <result column="notify_content" property="notifyContent" jdbcType="LONGVARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        trade_no,
        tenant_code,
        user_id,
        pay_no,
        channel,
        order_no,
        pay_price,
        pay_status,
        pay_desc,
        pay_time,
        pay_type,
        busi_type,
        refund_no,
        update_by,
        remark
    </sql>
    <sql id="Blob_Column_List">
        notify_content
    </sql>
    <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from pay_trade_record
        where trade_no = #{tradeNo,jdbcType=VARCHAR}
    </select>

    <select id="selectByOrderNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from pay_trade_record
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>

    <select id="selectByRefundNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from pay_trade_record
        where refund_no = #{refundNo,jdbcType=VARCHAR}
    </select>

    <select id="countPayPrice" resultType="com.tydic.nbchat.pay.mapper.po.PayPriceCountResult">
        SELECT monthlyAmount.totalAmount        AS monthlyTotalAmount,
               monthlyAmount.personAmount       AS monthlyPersonAmount,
               monthlyAmount.personFirstAmount  AS monthlyPersonFirstAmount,
               monthlyAmount.personRepeatAmount AS monthlyPersonRepeatAmount,
               monthlyAmount.enterpriseAmount   AS monthlyEnterpriseAmount,
               weeklyAmount.totalAmount         AS weeklyTotalAmount,
               weeklyAmount.personAmount        AS weeklyPersonAmount,
               weeklyAmount.personFirstAmount   AS weeklyPersonFirstAmount,
               weeklyAmount.personRepeatAmount  AS weeklyPersonRepeatAmount,
               weeklyAmount.enterpriseAmount    AS weeklyEnterpriseAmount,
               dailyAmount.totalAmount          AS dailyTotalAmount,
               dailyAmount.personAmount         AS dailyPersonAmount,
               dailyAmount.personFirstAmount    AS dailyPersonFirstAmount,
               dailyAmount.personRepeatAmount   AS dailyPersonRepeatAmount,
               dailyAmount.enterpriseAmount     AS dailyEnterpriseAmount
        FROM (SELECT ifnull(sum(t.pay_price), 0)                                                                                                                  AS totalAmount,
                     sum(if(t.tenant_code = '00000000', t.pay_price, 0))                                                                                          AS personAmount,
                     sum(if(t.tenant_code = '00000000' AND t.pay_time = (SELECT min(pay_time) FROM pay_trade_record WHERE user_id = t.user_id), t.pay_price, 0))  AS personFirstAmount,
                     sum(if(t.tenant_code = '00000000' AND t.pay_time != (SELECT min(pay_time) FROM pay_trade_record WHERE user_id = t.user_id), t.pay_price, 0)) AS personRepeatAmount,
                     sum(if(t.tenant_code = '00000000', 0, t.pay_price))                                                                                          AS enterpriseAmount
              FROM pay_trade_record t
              WHERE t.pay_type != 'DOU'
                AND date_format(t.pay_time, '%Y-%m') = date_format(now(), '%Y-%m')
                AND t.refund_no = '') AS monthlyAmount,
             (SELECT ifnull(sum(t.pay_price), 0)                                                                                                                  AS totalAmount,
                     sum(if(t.tenant_code = '00000000', t.pay_price, 0))                                                                                          AS personAmount,
                     sum(if(t.tenant_code = '00000000' AND t.pay_time = (SELECT min(pay_time) FROM pay_trade_record WHERE user_id = t.user_id), t.pay_price, 0))  AS personFirstAmount,
                     sum(if(t.tenant_code = '00000000' AND t.pay_time != (SELECT min(pay_time) FROM pay_trade_record WHERE user_id = t.user_id), t.pay_price, 0)) AS personRepeatAmount,
                     sum(if(t.tenant_code = '00000000', 0, t.pay_price))                                                                                          AS enterpriseAmount
              FROM pay_trade_record t
              WHERE t.pay_type != 'DOU'
                AND yearweek(t.pay_time, 1) = yearweek(curdate(), 1)
                AND t.refund_no = '') AS weeklyAmount,
             (SELECT ifnull(sum(t.pay_price), 0)                                                                                                                  AS totalAmount,
                     sum(if(t.tenant_code = '00000000', t.pay_price, 0))                                                                                          AS personAmount,
                     sum(if(t.tenant_code = '00000000' AND t.pay_time = (SELECT min(pay_time) FROM pay_trade_record WHERE user_id = t.user_id), t.pay_price, 0))  AS personFirstAmount,
                     sum(if(t.tenant_code = '00000000' AND t.pay_time != (SELECT min(pay_time) FROM pay_trade_record WHERE user_id = t.user_id), t.pay_price, 0)) AS personRepeatAmount,
                     sum(if(t.tenant_code = '00000000', 0, t.pay_price))                                                                                          AS enterpriseAmount
              FROM pay_trade_record t
              WHERE t.pay_type != 'DOU'
                AND date(t.pay_time) = curdate()
                AND t.refund_no = '') AS dailyAmount
    </select>


    <select id="selectPayTradeRecord" resultType="com.tydic.nbchat.pay.mapper.po.PayOpDataSelectResult"
            parameterType="com.tydic.nbchat.pay.mapper.po.PayOpDataSelectCondition">
        SELECT *
        FROM (SELECT ptr.order_no AS orderNo,
        ptr.trade_no AS tradeNo,
        ptr.user_id AS userId,
        t_order.order_status AS orderStatus,
        ptr.pay_type AS payType,
        ptr.pay_no AS payNo,
        sku.sku_id AS skuId,
        sku.sku_name AS skuName,
        sku.sku_desc AS skuDesc,
        t_order.create_time AS orderTime,
        ptr.pay_time AS payTime,
        t_order.order_price AS orderPrice,
        ptr.pay_price AS payPrice,
        if(t_order.order_price > ptr.pay_price, t_order.order_price - ptr.pay_price, 0) AS discountAmount,
        ptr.tenant_code AS tenantCode,
        nsut.user_reality_name AS userRealityName,
        nst.tenant_name AS tenantName,
        nu.phone,
        nu.created_time AS createdTime,
        orud.reg_channel AS regChannel,
        sum(if(ptr.refund_no = '', ptr.pay_price, 0)) OVER () AS totalPayPrice,
        rupd.points,
        coupon.name AS couponName,
        if(t_order.order_status = '5' OR spu.spu_id = '0000', ptr.update_by, NULL) AS updateBy,
        ptr.remark AS remark,
        if(ptr.tenant_code = '00000000', '0', '1') AS paymentType,
        CASE
        WHEN ptr.pay_time = (SELECT min(pay_time)
        FROM pay_trade_record
        WHERE user_id = ptr.user_id)
        THEN if(datediff(ptr.pay_time, nu.created_time) <![CDATA[<=]]> 7, '0', '1')
        WHEN spu.spu_type IN ('2', '3', '4', '5') THEN '2'
        WHEN ptr.pay_time <![CDATA[<]]> orud.vip_first_time THEN '3'
        WHEN ptr.pay_time > date_add(orud.vip_end_time, INTERVAL 7 DAY) THEN '5'
        ELSE '4' END AS paymentAttr,
        t_order.create_time AS createTime
        FROM pay_trade_record ptr
        JOIN (SELECT order_no,
        sku_id,
        order_status,
        create_time,
        order_price
        FROM tdh_customize_record
        UNION ALL
        SELECT po.order_no,
        sku_id,
        order_status,
        create_time,
        price
        FROM pay_order po
        JOIN pay_order_item poi
        ON po.order_no = poi.order_no) t_order
        ON ptr.order_no = t_order.order_no
        JOIN pay_goods_sku sku
        ON t_order.sku_id = sku.sku_id
        JOIN pay_goods_spu spu
        ON sku.spu_id = spu.spu_id
        JOIN nbchat_sys_tenant nst
        ON ptr.tenant_code = nst.tenant_code
        LEFT JOIN nbchat_sys_user_tenant nsut
        ON ptr.tenant_code = nsut.tenant_code
        AND ptr.user_id = nsut.user_id
        LEFT JOIN nbchat_user nu
        ON ptr.user_id = nsut.user_id
        LEFT JOIN op_rp_user_detail orud
        ON ptr.tenant_code = orud.tenant_code
        AND ptr.user_id = orud.user_id
        LEFT JOIN (SELECT order_no, sum(points) AS points
        FROM rebate_user_point_detail
        WHERE type = '1'
        GROUP BY order_no) rupd
        ON rupd.order_no = ptr.order_no
        LEFT JOIN (SELECT ruc.order_no, rcc.name
        FROM rebate_user_coupon ruc
        JOIN rebate_coupon_config rcc
        ON rcc.config_id = ruc.config_id
        WHERE status = '1') coupon
        ON coupon.order_no = ptr.order_no
        WHERE ptr.pay_type != 'DOU'
        <if test="orderStatus != null and orderStatus != ''">
            AND t_order.order_status = #{orderStatus,jdbcType=VARCHAR}
        </if>
        <if test="targetTenant != null and targetTenant != ''">
            AND ptr.tenant_code = #{targetTenant,jdbcType=VARCHAR}
        </if>
        <if test="skuId != null and skuId != ''">
            AND sku.sku_id = #{skuId,jdbcType=VARCHAR}
        </if>
        <if test="goodsName != null and goodsName != ''">
            AND sku.sku_name LIKE CONCAT('%', #{goodsName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="payStartTime != null and payEndTime != null">
            AND ptr.pay_time BETWEEN #{payStartTime,jdbcType=TIMESTAMP} AND #{payEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="phone != null and phone != ''">
            AND nu.phone = #{phone,jdbcType=VARCHAR}
        </if>
        <if test="targetUserId != null and targetUserId != ''">
            AND ptr.user_id = #{targetUserId,jdbcType=VARCHAR}
        </if>
        ) t
        <where>
            <if test="paymentType != null and paymentType != ''">
                AND paymentType = #{paymentType,jdbcType=VARCHAR}
            </if>
            <if test="paymentAttr != null and paymentAttr != ''">
                AND paymentAttr = #{paymentAttr,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY createTime DESC
    </select>
    <select id="selectPayTradeRecordByTdh" resultType="com.tydic.nbchat.pay.mapper.po.PayOpDataSelectResult"
            parameterType="com.tydic.nbchat.pay.mapper.po.PayOpDataSelectCondition">
        <!-- 数字人和声音定制统计 -->
        SELECT *
        FROM (
        SELECT a.order_no AS orderNo,
        d.trade_no as tradeNm,
        a.user_id AS userId,
        a.order_status AS orderStatus,
        d.pay_type AS payType,
        d.pay_no AS payNo,
        b.sku_id AS skuId,
        b.sku_name AS skuName,
        b.sku_desc AS skuDesc,
        a.create_time AS orderTime,
        d.pay_time AS payTime,
        a.order_price AS orderPrice,
        a.pay_price AS payPrice,
        if(a.order_price > a.pay_price, a.order_price - a.pay_price, 0) AS discountAmount,
        e.tenant_code AS tenantCode,
        e.user_reality_name AS userRealityName,
        f.tenant_name AS tenantName,
        g.phone,
        g.created_time AS createdTime,
        h.reg_channel AS regChannel,
        sum(if(d.refund_no = '', a.pay_price, 0)) OVER () AS totalPayPrice,
        i.points,
        j.name AS couponName,
        if(a.order_status = '5' or c.spu_id = '0000', d.update_by, null) AS updateBy,
        d.remark AS remark,
        if(e.tenant_code = '00000000', '0', '1') AS paymentType,
        CASE
        WHEN d.pay_time = (SELECT min(pay_time)
        FROM pay_trade_record
        WHERE user_id = a.user_id)
        THEN if(datediff(d.pay_time, g.created_time) &lt;= 7, '0', '1')
        WHEN c.spu_type in ('2','3','4','5') THEN '2'
        WHEN d.pay_time &lt; h.vip_first_time THEN '3'
        WHEN d.pay_time > date_add(h.vip_end_time, INTERVAL 7 DAY) THEN '5'
        ELSE '4' END AS paymentAttr,
        a.create_time AS createTime
        FROM tdh_customize_record a
        JOIN pay_goods_sku b
        ON a.sku_id = b.sku_id
        JOIN pay_goods_spu c
        ON b.spu_id = c.spu_id
        JOIN pay_trade_record d
        ON a.order_no = d.order_no
        JOIN nbchat_sys_user_tenant e
        ON a.user_id = e.user_id
        AND a.tenant_code = e.tenant_code
        JOIN nbchat_sys_tenant f
        ON a.tenant_code = f.tenant_code
        JOIN nbchat_user g
        ON a.user_id = g.user_id
        JOIN op_rp_user_detail h
        ON a.user_id = h.user_id
        AND a.tenant_code = h.tenant_code
        LEFT JOIN
        (SELECT order_no, sum(points) AS points
        FROM rebate_user_point_detail
        WHERE type = '1'
        GROUP BY order_no) i
        ON i.order_no = a.order_no
        LEFT JOIN
        (SELECT ruc.order_no, rcc.name
        FROM rebate_user_coupon ruc
        JOIN rebate_coupon_config rcc
        ON rcc.config_id = ruc.config_id
        WHERE status = '1') j
        ON j.order_no = a.order_no
        where d.pay_type != 'DOU'
        <if test="orderStatus != null and orderStatus != ''">
            AND a.order_status = #{orderStatus}
        </if>
        <if test="targetTenant != null and targetTenant != ''">
            AND e.tenant_code = #{targetTenant}
        </if>
        <if test="skuId != null and skuId != ''">
            AND a.sku_id = #{skuId}
        </if>
        <if test="goodsName != null and goodsName != ''">
            AND b.sku_name LIKE CONCAT('%', #{goodsName}, '%')
        </if>
        <if test="payStartTime != null and payEndTime != null">
            AND d.pay_time BETWEEN #{payStartTime} AND #{payEndTime}
        </if>
        <if test="phone != null and phone != ''">
            AND g.phone = #{phone}
        </if>
        <if test="targetUserId != null and targetUserId != ''">
            AND g.user_id = #{targetUserId}
        </if>
        ) t
        <where>
            <if test="paymentType != null and paymentType != ''">
                AND paymentType = #{paymentType}
            </if>
            <if test="paymentAttr != null and paymentAttr != ''">
                AND paymentAttr = #{paymentAttr}
            </if>
        </where>
        ORDER BY createTime DESC
    </select>
    <select id="selectPayTradeRecordByVip" resultType="com.tydic.nbchat.pay.mapper.po.PayOpDataSelectResult"
            parameterType="com.tydic.nbchat.pay.mapper.po.PayOpDataSelectCondition">
        <!-- 会员统计 -->
        SELECT *
        FROM (SELECT d.sku_name AS skuName,
        b.pay_price AS payPrice,
        c.order_status AS orderStatus,
        b.user_id AS userId,
        b.order_no AS orderNo,
        b.pay_type AS payType,
        b.pay_no AS payNo,
        a.sku_id AS skuId,
        d.sku_desc AS skuDesc,
        b.pay_time AS payTime,
        a.create_time AS orderTime,
        a.price AS orderPrice,
        b.tenant_code AS tenantCode,
        g.user_reality_name AS userRealityName,
        f.tenant_name AS tenantName,
        h.phone,
        h.created_time AS createdTime,
        i.reg_channel AS regChannel,
        sum(if(b.refund_no = '', b.pay_price, 0)) OVER () AS totalPayPrice,
        if(a.price > b.pay_price, a.price - b.pay_price, 0) AS discountAmount,
        j.points,
        k.name AS couponName,
        if(c.order_status = '5' or e.spu_id = '0000', b.update_by, null) AS updateBy,
        b.remark AS remark,
        if(c.tenant_code = '00000000', '0', '1') AS paymentType,
        CASE
        WHEN b.pay_time = (SELECT min(pay_time)
        FROM pay_trade_record
        WHERE user_id = b.user_id)
        THEN if(datediff(b.pay_time, h.created_time) &lt;= 7, '0', '1')
        WHEN e.spu_type in ('2', '3', '4', '5') THEN '2'
        WHEN b.pay_time &lt; i.vip_first_time THEN '3'
        WHEN b.pay_time > date_add(i.vip_end_time, INTERVAL 7 DAY) THEN '5'
        ELSE '4' END AS paymentAttr
        FROM pay_order_item a
        JOIN pay_trade_record b
        ON a.order_no = b.order_no
        JOIN pay_order c
        ON a.order_no = c.order_no
        JOIN pay_goods_sku d
        ON a.sku_id = d.sku_id
        JOIN pay_goods_spu e
        ON d.spu_id = e.spu_id
        JOIN nbchat_sys_tenant f
        ON b.tenant_code = f.tenant_code
        LEFT JOIN nbchat_sys_user_tenant g
        ON b.user_id = g.user_id
        AND g.tenant_code = b.tenant_code
        LEFT JOIN nbchat_user h
        ON b.user_id = h.user_id
        LEFT JOIN op_rp_user_detail i
        ON b.user_id = i.user_id
        AND i.tenant_code = b.tenant_code
        LEFT JOIN (SELECT order_no, sum(points) AS points
        FROM rebate_user_point_detail
        WHERE type = '1'
        GROUP BY order_no) j
        ON j.order_no = b.order_no
        LEFT JOIN (SELECT ruc.order_no, rcc.name
        FROM rebate_user_coupon ruc
        JOIN rebate_coupon_config rcc
        ON rcc.config_id = ruc.config_id
        WHERE status = '1') k
        ON k.order_no = b.order_no
        where b.pay_type != 'DOU'
        <if test="orderStatus != null and orderStatus != ''">
            AND order_status = #{orderStatus}
        </if>
        <if test="targetTenant != null and targetTenant != ''">
            AND b.tenant_code = #{targetTenant}
        </if>
        <if test="skuId != null and skuId != ''">
            AND a.sku_id = #{skuId}
        </if>
        <if test="goodsName != null and goodsName != ''">
            AND a.sku_name LIKE CONCAT('%', #{goodsName}, '%')
        </if>
        <if test="payStartTime != null and payEndTime != null">
            AND b.pay_time BETWEEN #{payStartTime} AND #{payEndTime}
        </if>
        <if test="phone != null and phone != ''">
            AND h.phone = #{phone}
        </if>
        <if test="targetUserId != null and targetUserId != ''">
            AND b.user_id = #{targetUserId}
        </if>
        ) t
        <where>
            <if test="paymentType != null and paymentType != ''">
                AND paymentType = #{paymentType}
            </if>
            <if test="paymentAttr != null and paymentAttr != ''">
                AND paymentAttr = #{paymentAttr}
            </if>
        </where>
        ORDER BY payTime DESC
    </select>
    <insert id="insertSelective" parameterType="com.tydic.nbchat.pay.mapper.po.PayTradeRecord">
        insert into pay_trade_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tradeNo != null">
                trade_no,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="payNo != null">
                pay_no,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="payPrice != null">
                pay_price,
            </if>
            <if test="payStatus != null">
                pay_status,
            </if>
            <if test="payDesc != null">
                pay_desc,
            </if>
            <if test="payTime != null">
                pay_time,
            </if>
            <if test="payType != null">
                pay_type,
            </if>
            <if test="notifyContent != null">
                notify_content,
            </if>
            <if test="busiType != null">
                busi_type,
            </if>
            <if test="refundNo != null">
                refund_no,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tradeNo != null">
                #{tradeNo,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="payNo != null">
                #{payNo,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                #{channel,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="payPrice != null">
                #{payPrice,jdbcType=INTEGER},
            </if>
            <if test="payStatus != null">
                #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDesc != null">
                #{payDesc,jdbcType=VARCHAR},
            </if>
            <if test="payTime != null">
                #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payType != null">
                #{payType,jdbcType=CHAR},
            </if>
            <if test="notifyContent != null">
                #{notifyContent,jdbcType=LONGVARCHAR},
            </if>
            <if test="busiType != null">
                #{busiType},
            </if>
            <if test="refundNo != null">
                #{refundNo},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
        </trim>
    </insert>
    <update id="updateByTradeNo" parameterType="com.tydic.nbchat.pay.mapper.po.PayTradeRecord">
        update pay_trade_record
        <set>
            <if test="tenantCode != null">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="payNo != null">
                pay_no = #{payNo,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                channel = #{channel,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="payPrice != null">
                pay_price = #{payPrice,jdbcType=INTEGER},
            </if>
            <if test="payStatus != null">
                pay_status = #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDesc != null">
                pay_desc = #{payDesc,jdbcType=VARCHAR},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payType != null">
                pay_type = #{payType,jdbcType=CHAR},
            </if>
            <if test="notifyContent != null">
                notify_content = #{notifyContent,jdbcType=LONGVARCHAR},
            </if>
            <if test="refundNo != null">
                refund_no = #{refundNo,jdbcType=CHAR},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=CHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=CHAR},
            </if>
        </set>
        where trade_no = #{tradeNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByOrderNoSelective" parameterType="com.tydic.nbchat.pay.mapper.po.PayTradeRecord">
        update pay_trade_record
        <set>
            <if test="tenantCode != null">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="payNo != null">
                pay_no = #{payNo,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                channel = #{channel,jdbcType=VARCHAR},
            </if>
            <if test="tradeNo != null">
                trade_no = #{tradeNo,jdbcType=VARCHAR},
            </if>
            <if test="payPrice != null">
                pay_price = #{payPrice,jdbcType=INTEGER},
            </if>
            <if test="payStatus != null">
                pay_status = #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDesc != null">
                pay_desc = #{payDesc,jdbcType=VARCHAR},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payType != null">
                pay_type = #{payType,jdbcType=CHAR},
            </if>
            <if test="notifyContent != null">
                notify_content = #{notifyContent,jdbcType=LONGVARCHAR},
            </if>
            <if test="refundNo != null">
                refund_no = #{refundNo,jdbcType=CHAR},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=CHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=CHAR},
            </if>
        </set>
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </update>
</mapper>
