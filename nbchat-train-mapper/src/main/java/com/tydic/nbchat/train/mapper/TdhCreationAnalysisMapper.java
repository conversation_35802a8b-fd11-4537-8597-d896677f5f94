package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.TdhCreationAnalysis;

import java.util.List;

/**
 * 视频分析数据(TdhCreationAnalysis)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-28 15:52:54
 */
public interface TdhCreationAnalysisMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param taskId 主键
     * @return 实例对象
     */
    TdhCreationAnalysis queryById(String taskId);

    List<TdhCreationAnalysis> selectAll(TdhCreationAnalysis tdhCreationAnalysis);

    /**
     * 新增数据
     *
     * @param tdhCreationAnalysis 实例对象
     * @return 影响行数
     */
    int insert(TdhCreationAnalysis tdhCreationAnalysis);


    int insertSelective(TdhCreationAnalysis tdhCreationAnalysis);

      /**
     * 修改数据
     *
     * @param tdhCreationAnalysis 实例对象
     * @return 影响行数
     */
    int update(TdhCreationAnalysis tdhCreationAnalysis);

    /**
     * 通过主键删除数据
     *
     * @param taskId 主键
     * @return 影响行数
     */
    int deleteById(String taskId);

}

