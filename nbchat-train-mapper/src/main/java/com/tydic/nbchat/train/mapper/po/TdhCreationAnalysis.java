package com.tydic.nbchat.train.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 视频分析数据(TdhCreationAnalysis)实体类
 *
 * <AUTHOR>
 * @since 2025-05-28 15:52:54
 */
@Data
public class TdhCreationAnalysis implements Serializable {
    private static final long serialVersionUID = -93743082012501833L;
/**
     * 任务id
     */
    private String taskId;
/**
     * 大模型返回json
     */
    private String videoClass;
/**
     * 视频使用场景 一级分类
     */
    private String sceneCategory;
/**
     * 视频使用场景 二级分类
     */
    private String sceneCategorySub;
/**
     * 视频内容分类 一级分类
     */
    private String contentCategory;
/**
     * 视频内容分类 二级分类
     */
    private String contentCategorySub;
/**
     * 视频vip类型
     */
    private String vipType;
/**
     * 视频vip类型子类: 0=临时卡，1=月卡，2=季卡，3=年卡
     */
    private String vipTypeSub;
/**
     * 距离创作日已开通天数
     */
    private Integer vipUseDays;
/**
     * 距离创作日已注册天数
     */
    private Integer registerDays;
/**
     * 创建时间
     */
    private Date createTime;
/**
     * 更新时间
     */
    private Date updateTime;
/**
     * 分析类型：0视频 1ppt
     */
    private String analysisType;

}

