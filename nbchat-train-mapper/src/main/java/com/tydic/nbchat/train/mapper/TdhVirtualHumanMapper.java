package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.TdhVirtualHuman;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数字人-虚拟人形象(TdhVirtualHuman)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-22 15:51:12
 */
public interface TdhVirtualHumanMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param tdhId 主键
     * @return 实例对象
     */
    TdhVirtualHuman queryById(String tdhId);

    /**
     * 查询多条数据
     * @param tdhVirtualHuman
     * @return
     */
    List<TdhVirtualHuman> selectAll(TdhVirtualHuman tdhVirtualHuman);

    /**
     * 批量查询多条
     * @param ids
     * @return
     */
    List<TdhVirtualHuman> selectByIds(@Param("ids")List<String> ids);

    /**
     * 根据模板ID列表查询数字人信息
     * @param tplIds 模板ID列表
     * @return 数字人列表
     */
    List<TdhVirtualHuman> selectByTplIds(@Param("tplIds")List<String> tplIds);

    /**
     * 运营平台查询数字人列表
     */
    List<TdhVirtualHuman> selectByConditions(TdhVirtualHuman tdhVirtualHuman);

    /**
     * 新增数据
     *
     * @param tdhVirtualHuman 实例对象
     * @return 影响行数
     */
    int insert(TdhVirtualHuman tdhVirtualHuman);


    int insertSelective(TdhVirtualHuman tdhVirtualHuman);

      /**
     * 修改数据
     *
     * @param tdhVirtualHuman 实例对象
     * @return 影响行数
     */
    int update(TdhVirtualHuman tdhVirtualHuman);

    /**
     * 根据订单号修改数据
     * @param tdhVirtualHuman
     * @return
     */
    int updateByOrderNo(TdhVirtualHuman tdhVirtualHuman);

    /**
     * 通过主键删除数据
     *
     * @param tdhId 主键
     * @return 影响行数
     */
    int deleteById(String tdhId);

    TdhVirtualHuman findByOrderNoAndUserId(@Param("orderNo")String orderNo,@Param("userId")String userId);

    int updateBatchSelective(@Param("list") List<TdhVirtualHuman> list);

}

