<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.PptCreationRecordMapper">

    <resultMap type="com.tydic.nbchat.train.mapper.po.PptCreationRecord" id="PptCreationRecordMap">
        <result property="pptId" column="ppt_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="themeId" column="theme_id" jdbcType="VARCHAR"/>
        <result property="creationName" column="creation_name" jdbcType="VARCHAR"/>
        <result property="titleContent" column="title_content" jdbcType="VARCHAR"/>
        <result property="creationContent" column="creation_content" jdbcType="VARCHAR"/>
        <result property="partCount" column="part_count" jdbcType="INTEGER"/>
        <result property="aiContent" column="ai_content" jdbcType="VARCHAR"/>
        <result property="previewUrl" column="preview_url" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="fileId" column="file_id" jdbcType="VARCHAR"/>
        <result property="fileVector" column="file_vector" jdbcType="VARCHAR"/>
        <result property="isShare" column="is_share" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="pptType" column="ppt_type" jdbcType="VARCHAR"/>
        <result property="layout" column="layout" jdbcType="VARCHAR"/>
        <result property="creationType" column="creation_type" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="pptListMap" type="com.tydic.nbchat.train.mapper.po.NbchatPptDTO">
        <id property="pptId" column="ppt_id"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="userId" column="user_id"/>
        <result property="themeId" column="theme_id"/>
        <result property="creationName" column="creation_name"/>
        <result property="titleContent" column="title_content"/>
        <result property="partCount" column="part_count"/>
        <result property="aiContent" column="ai_content"/>
        <result property="previewUrl" column="preview_url"/>
        <result property="isValid" column="is_valid"/>
        <result property="isShare" column="is_share"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="fileUrl" column="file_url"/>
        <result property="fileId" column="file_id"/>
        <result property="fileVector" column="file_vector"/>
        <result property="layout" column="layout"/>
        <result property="pptType" column="ppt_type"/>
        <result property="creationType" column="creation_type"/>
        <result property="userName" column="user_name"/>
        <result property="phone" column="phone"/>
        <result property="companyName" column="company_name"/>
        <result property="vipStatus" column="vip_status"/>
        <result property="userType" column="user_type"/>
        <result property="vipType" column="vip_type" jdbcType="VARCHAR"/>
        <result property="vipTypeSub" column="vip_type_sub" jdbcType="VARCHAR"/>
        <result property="vipUseDays" column="vip_use_days" jdbcType="INTEGER"/>
        <result property="registerDays" column="register_days" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        ppt_id, tenant_code, user_id, theme_id, creation_name, title_content, creation_content, part_count, ai_content,preview_url,
      is_valid, is_share,creation_type, create_time, update_time,file_url,file_id,file_vector,ppt_type,layout</sql>

    <sql id="Base_Simple_Column_List">
        ppt_id, tenant_code, user_id, theme_id, creation_name, title_content, part_count, ai_content,preview_url,
      is_valid, is_share,creation_type, create_time, update_time,file_url,file_id,file_vector,ppt_type,layout</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="PptCreationRecordMap">
        select
          <include refid="Base_Column_List" />
        from ppt_creation_record
        where ppt_id = #{pptId}
    </select>


    <select id="selectAll" resultMap="PptCreationRecordMap" parameterType="com.tydic.nbchat.train.mapper.po.PptCreationRecord">
        select
        <if test="pptId != null and pptId != ''">
            <include refid="Base_Column_List"/>
        </if>
        <if test="pptId == null or pptId == ''">
            <include refid="Base_Simple_Column_List"/>
        </if>
        from ppt_creation_record
        <where>
            <if test="pptId != null and pptId != ''">
                and ppt_id = #{pptId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="themeId != null and themeId != ''">
                and theme_id = #{themeId}
            </if>
            <if test="creationName != null and creationName != ''">
                and creation_name = #{creationName}
            </if>
            <if test="titleContent != null and titleContent != ''">
                and title_content = #{titleContent}
            </if>
            <if test="creationContent != null and creationContent != ''">
                and creation_content = #{creationContent}
            </if>
            <if test="partCount != null">
                and part_count = #{partCount}
            </if>
            <if test="aiContent != null and aiContent != ''">
                and ai_content = #{aiContent}
            </if>
            <if test="previewUrl != null and previewUrl != ''">
                and preview_url = #{previewUrl}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
            <if test="isShare != null and isShare != ''">
                and is_share = #{isShare}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="fileVector != null and fileVector != ''">
                and file_vector = #{fileVector}
            </if>
            <if test="pptType != null and pptType != ''">
                and ppt_type = #{pptType}
            </if>
        </where>
        order by update_time desc
    </select>


    <insert id="insertSelective" keyProperty="pptId" useGeneratedKeys="true" parameterType="com.tydic.nbchat.train.mapper.po.PptCreationRecord">
        insert into ppt_creation_record
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="pptId != null and pptId != ''">
                ppt_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="themeId != null and themeId != ''">
                theme_id,
            </if>
            <if test="creationName != null and creationName != ''">
                creation_name,
            </if>
            <if test="titleContent != null and titleContent != ''">
                title_content,
            </if>
            <if test="creationContent != null and creationContent != ''">
                creation_content,
            </if>
            <if test="partCount != null">
                part_count,
            </if>
            <if test="aiContent != null and aiContent != ''">
                ai_content,
            </if>
            <if test="previewUrl != null and previewUrl != ''">
                preview_url,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
            <if test="isShare != null and isShare != ''">
                is_share,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                file_url,
            </if>
            <if test="fileId != null and fileId != ''">
                file_id,
            </if>
            <if test="pptType != null and pptType != ''">
                ppt_type,
            </if>
            <if test="layout != null and layout != ''">
                layout,
            </if>
            <if test="creationType != null and creationType != ''">
                creation_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="pptId != null and pptId != ''">
                #{pptId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="themeId != null and themeId != ''">
                #{themeId},
            </if>
            <if test="creationName != null and creationName != ''">
                #{creationName},
            </if>
            <if test="titleContent != null and titleContent != ''">
                #{titleContent},
            </if>
            <if test="creationContent != null and creationContent != ''">
                #{creationContent},
            </if>
            <if test="partCount != null">
                #{partCount},
            </if>
            <if test="aiContent != null and aiContent != ''">
                #{aiContent},
            </if>
            <if test="previewUrl != null and previewUrl != ''">
                #{previewUrl},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid},
            </if>
            <if test="isShare != null and isShare != ''">
                #{isShare},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                #{fileUrl},
            </if>
            <if test="fileId != null and fileId != ''">
                #{fileId},
            </if>
            <if test="pptType != null and pptType != ''">
                #{pptType},
            </if>
            <if test="layout != null and layout != ''">
                #{layout},
            </if>
            <if test="creationType != null and creationType != ''">
                #{creationType},
            </if>
        </trim>
    </insert>


    <!--通过主键修改数据-->
    <update id="update">
        update ppt_creation_record
        <set>
            <if test="themeId != null and themeId != ''">
                theme_id = #{themeId},
            </if>
            <if test="creationName != null and creationName != ''">
                creation_name = #{creationName},
            </if>
            <if test="titleContent != null and titleContent != ''">
                title_content = #{titleContent},
            </if>
            <if test="creationContent != null and creationContent != ''">
                creation_content = #{creationContent},
            </if>
            <if test="partCount != null">
                part_count = #{partCount},
            </if>
            <if test="aiContent != null and aiContent != ''">
                ai_content = #{aiContent},
            </if>
            <if test="previewUrl != null and previewUrl != ''">
                preview_url = #{previewUrl},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
            <if test="isShare != null and isShare != ''">
                is_share = #{isShare},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                file_url = #{fileUrl},
            </if>
            <if test="fileId != null and fileId != ''">
                file_id = #{fileId},
            </if>
            <if test="fileVector != null and fileVector != ''">
                file_vector = #{fileVector},
            </if>
        </set>
        where ppt_id = #{pptId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ppt_creation_record where ppt_id = #{pptId}
    </delete>

    <select id="getPptCount" resultType="int">
        SELECT COUNT(*)
        FROM ppt_creation_record
        WHERE
        <choose>
            <!-- 如果没有传入参数或传入0，查询当天的数据 -->
            <when test="timeRange == 0 or timeRange == null">
                DATE(create_time) = CURDATE()
            </when>
            <!-- 如果传入了数字（29或6等），查询前一天开始，直到这个数字之前的数据 -->
            <when test="timeRange != 0 and timeRange != null">
                create_time >= DATE_SUB(CURDATE(), INTERVAL #{timeRange} DAY)
                AND create_time &lt; DATE_SUB(CURDATE(), INTERVAL 0 DAY)
            </when>
            <!-- 默认情况下查询当天的数据 -->
            <otherwise>
                DATE(create_time) = CURDATE()
            </otherwise>
        </choose>
        <choose>
            <when test="userType != null and userType == 0">
                AND tenant_code = '00000000'
            </when>
            <when test="userType == null or userType == ''">
                <!-- 不添加任何 SQL -->
            </when>
            <otherwise>
                AND tenant_code != '00000000'
            </otherwise>
        </choose>
    </select>

    <select id="getPptList" resultMap="pptListMap">
        SELECT
        ppt.ppt_id,
        ppt.tenant_code,
        ppt.user_id,
        ppt.theme_id,
        ppt.creation_name,
        ppt.title_content,
        ppt.part_count,
        ppt.ai_content,
        ppt.preview_url,
        ppt.is_valid,
        ppt.is_share,
        ppt.create_time,
        ppt.update_time,
        ppt.file_url,
        ppt.file_id,
        ppt.file_vector,
        ppt.layout,
        ppt.ppt_type,
        ppt.creation_type,
        u.user_name,
        u.phone,
        u.company_name,
        u.vip_status,
        u.user_type,
        t2.vip_type,
        t2.vip_type_sub,
        t2.vip_use_days,
        t2.register_days
        FROM
        ppt_creation_record ppt
        LEFT JOIN
        op_rp_user_detail u ON ppt.user_id = u.user_id AND ppt.tenant_code = u.tenant_code
        LEFT JOIN tdh_creation_analysis t2 on ppt.ppt_id = t2.task_id
        <where>
            <if test="pptType != null and pptType != ''">
                AND ppt.ppt_type = #{pptType}
            </if>
            <if test="startTime != null">
                AND ppt.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ppt.create_time &lt;= #{endTime}
            </if>
            <if test="minPartCount != null">
                AND ppt.part_count >= #{minPartCount}
            </if>
            <if test="maxPartCount != null">
                AND ppt.part_count &lt;= #{maxPartCount}
            </if>
            <if test="phone != null and phone != ''">
                AND u.phone = #{phone}
            </if>
            <if test="isPay == '1'.toString()">
                and u.total_pay_amount > 0
            </if>
            <if test="isPay == '0'.toString()">
                and u.total_pay_amount = 0
            </if>
            <if test="userType != null and userType != ''">
                AND u.user_type = #{userType}
            </if>
            <if test="companyName != null and companyName != ''">
                AND u.company_name LIKE CONCAT('%', #{companyName}, '%')
            </if>
            <if test="targetTenantCode != null and targetTenantCode != ''">
                AND ppt.tenant_code = #{targetTenantCode}
            </if>
            <if test="creationTypeList != null and !creationTypeList.isEmpty()">
                AND ppt.creation_type IN
                <foreach item="item" collection="creationTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
        ppt.create_time DESC,
        ppt.part_count DESC
    </select>

</mapper>

