<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.TdhCreationAnalysisMapper">

    <resultMap type="com.tydic.nbchat.train.mapper.po.TdhCreationAnalysis" id="TdhCreationAnalysisMap">
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="videoClass" column="video_class" jdbcType="VARCHAR"/>
        <result property="sceneCategory" column="scene_category" jdbcType="VARCHAR"/>
        <result property="sceneCategorySub" column="scene_category_sub" jdbcType="VARCHAR"/>
        <result property="contentCategory" column="content_category" jdbcType="VARCHAR"/>
        <result property="contentCategorySub" column="content_category_sub" jdbcType="VARCHAR"/>
        <result property="vipType" column="vip_type" jdbcType="VARCHAR"/>
        <result property="vipTypeSub" column="vip_type_sub" jdbcType="VARCHAR"/>
        <result property="vipUseDays" column="vip_use_days" jdbcType="INTEGER"/>
        <result property="registerDays" column="register_days" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="analysisType" column="analysis_type" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        task_id
        , video_class, scene_category, scene_category_sub, content_category, content_category_sub, vip_type,
        vip_type_sub, vip_use_days, register_days, create_time, update_time, analysis_type
        </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="TdhCreationAnalysisMap">
        select
          <include refid="Base_Column_List" />
        from tdh_creation_analysis
        where task_id = #{taskId}
    </select>
    
    
    <select id="selectAll" resultMap="TdhCreationAnalysisMap" parameterType="com.tydic.nbchat.train.mapper.po.TdhCreationAnalysis">
        select
          <include refid="Base_Column_List" />
        from tdh_creation_analysis
         <where>
            <if test="taskId != null and taskId != ''">
                and task_id = #{taskId}
            </if>
            <if test="videoClass != null and videoClass != ''">
                and video_class = #{videoClass}
            </if>
            <if test="sceneCategory != null and sceneCategory != ''">
                and scene_category = #{sceneCategory}
            </if>
            <if test="sceneCategorySub != null and sceneCategorySub != ''">
                and scene_category_sub = #{sceneCategorySub}
            </if>
            <if test="contentCategory != null and contentCategory != ''">
                and content_category = #{contentCategory}
            </if>
            <if test="contentCategorySub != null and contentCategorySub != ''">
                and content_category_sub = #{contentCategorySub}
            </if>
            <if test="vipType != null and vipType != ''">
                and vip_type = #{vipType}
            </if>
            <if test="vipTypeSub != null and vipTypeSub != ''">
                and vip_type_sub = #{vipTypeSub}
            </if>
            <if test="vipUseDays != null">
                and vip_use_days = #{vipUseDays}
            </if>
            <if test="registerDays != null">
                and register_days = #{registerDays}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="analysisType != null and analysisType != ''">
                and analysis_type = #{analysisType}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="taskId" useGeneratedKeys="true" parameterType="com.tydic.nbchat.train.mapper.po.TdhCreationAnalysis">
        insert into tdh_creation_analysis
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="taskId != null and taskId != ''">
                task_id,
            </if>
            <if test="videoClass != null and videoClass != ''">
                video_class,
            </if>
            <if test="sceneCategory != null and sceneCategory != ''">
                scene_category,
            </if>
            <if test="sceneCategorySub != null and sceneCategorySub != ''">
                scene_category_sub,
            </if>
            <if test="contentCategory != null and contentCategory != ''">
                content_category,
            </if>
            <if test="contentCategorySub != null and contentCategorySub != ''">
                content_category_sub,
            </if>
            <if test="vipType != null and vipType != ''">
                vip_type,
            </if>
            <if test="vipTypeSub != null and vipTypeSub != ''">
                vip_type_sub,
            </if>
            <if test="vipUseDays != null">
                vip_use_days,
            </if>
            <if test="registerDays != null">
                register_days,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="analysisType != null and analysisType != ''">
                analysis_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="taskId != null and taskId != ''">
                #{taskId},
            </if>
            <if test="videoClass != null and videoClass != ''">
                #{videoClass},
            </if>
            <if test="sceneCategory != null and sceneCategory != ''">
                #{sceneCategory},
            </if>
            <if test="sceneCategorySub != null and sceneCategorySub != ''">
                #{sceneCategorySub},
            </if>
            <if test="contentCategory != null and contentCategory != ''">
                #{contentCategory},
            </if>
            <if test="contentCategorySub != null and contentCategorySub != ''">
                #{contentCategorySub},
            </if>
            <if test="vipType != null and vipType != ''">
                #{vipType},
            </if>
            <if test="vipTypeSub != null and vipTypeSub != ''">
                #{vipTypeSub},
            </if>
            <if test="vipUseDays != null">
                #{vipUseDays},
            </if>
            <if test="registerDays != null">
                #{registerDays},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="analysisType != null and analysisType != ''">
                #{analysisType},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="taskId" useGeneratedKeys="true">
        insert into tdh_creation_analysis(video_classscene_categoryscene_category_subcontent_categorycontent_category_subvip_typevip_type_subvip_use_daysregister_dayscreate_timeupdate_timeanalysis_type)
        values (#{videoClass}#{sceneCategory}#{sceneCategorySub}#{contentCategory}#{contentCategorySub}#{vipType}#{vipTypeSub}#{vipUseDays}#{registerDays}#{createTime}#{updateTime}#{analysisType})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tdh_creation_analysis
        <set>
            <if test="videoClass != null and videoClass != ''">
                video_class = #{videoClass},
            </if>
            <if test="sceneCategory != null and sceneCategory != ''">
                scene_category = #{sceneCategory},
            </if>
            <if test="sceneCategorySub != null and sceneCategorySub != ''">
                scene_category_sub = #{sceneCategorySub},
            </if>
            <if test="contentCategory != null and contentCategory != ''">
                content_category = #{contentCategory},
            </if>
            <if test="contentCategorySub != null and contentCategorySub != ''">
                content_category_sub = #{contentCategorySub},
            </if>
            <if test="vipType != null and vipType != ''">
                vip_type = #{vipType},
            </if>
            <if test="vipTypeSub != null and vipTypeSub != ''">
                vip_type_sub = #{vipTypeSub},
            </if>
            <if test="vipUseDays != null">
                vip_use_days = #{vipUseDays},
            </if>
            <if test="registerDays != null">
                register_days = #{registerDays},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="analysisType != null and analysisType != ''">
                analysis_type = #{analysisType},
            </if>
        </set>
        where task_id = #{taskId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from tdh_creation_analysis where task_id = #{taskId}
    </delete>

</mapper>

