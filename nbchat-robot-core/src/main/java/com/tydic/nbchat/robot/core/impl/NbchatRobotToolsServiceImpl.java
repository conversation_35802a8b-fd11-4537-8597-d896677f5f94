package com.tydic.nbchat.robot.core.impl;

import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.admin.api.SysRobotConfigApi;
import com.tydic.nbchat.robot.api.NbchatRobotToolsApi;
import com.tydic.nbchat.robot.api.RobotProcessCallback;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.RobotMsgContext;
import com.tydic.nbchat.robot.api.bo.eums.ChatAppType;
import com.tydic.nbchat.robot.api.bo.msg.ChatMessageBuilder;
import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.api.bo.tools.RobotToolsChatResponse;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionChoice;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionResult;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.core.helper.AppHelperFactory;
import com.tydic.nbchat.robot.core.helper.RobotAiHelperFactory;
import com.tydic.nbchat.robot.core.helper.SystemPresetPromptHelper;
import com.tydic.nbchat.robot.core.helper.api.AppHelper;
import com.tydic.nbchat.robot.core.helper.api.bo.AiRobotChatRequest;
import com.tydic.nbchat.robot.core.listener.NbchatMsgListener;
import com.tydic.nbchat.robot.core.listener.NbchatToolsListener;
import com.tydic.nbchat.robot.core.util.JsonUtil;
import com.tydic.nbchat.robot.core.util.NbchatRobotMsgBuilder;
import com.tydic.nbchat.robot.core.util.exception.SensitiveIncludeException;
import com.tydic.nbchat.robot.mapper.NbchatPresetPromptMapper;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
              group = "${nicc-dc-config.dubbo-provider.group}", timeout = 300000)
public class NbchatRobotToolsServiceImpl implements NbchatRobotToolsApi {

    final private NbchatRobotConfigProperties nbchatRobotConfigProperties;
    final private NbchatMsgListener nbchatMsgListener;
    final private SystemPresetPromptHelper systemPresetPromptHelper;
    final private RobotAiHelperFactory robotAiHelperFactory;
    final private NbchatToolsListener nbchatToolsListener;
    final private NbchatPresetPromptMapper nbchatPresetPromptMapper;
    final private AppHelperFactory appHelperFactory;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3 * 1000)
    private SysRobotConfigApi sysRobotConfigApi;

    public NbchatRobotToolsServiceImpl(NbchatRobotConfigProperties nbchatRobotConfigProperties,
                                       NbchatMsgListener nbchatMsgListener,
                                       SystemPresetPromptHelper systemPresetPromptHelper,
                                       RobotAiHelperFactory robotAiHelperFactory,
                                       NbchatToolsListener nbchatToolsListener,
                                       NbchatPresetPromptMapper nbchatPresetPromptMapper, AppHelperFactory appHelperFactory) {
        this.nbchatRobotConfigProperties = nbchatRobotConfigProperties;
        this.nbchatMsgListener = nbchatMsgListener;
        this.systemPresetPromptHelper = systemPresetPromptHelper;
        this.robotAiHelperFactory = robotAiHelperFactory;
        this.nbchatToolsListener = nbchatToolsListener;
        this.nbchatPresetPromptMapper = nbchatPresetPromptMapper;
        this.appHelperFactory = appHelperFactory;
    }

    private RobotMessageRequest buildMessageRequest(RobotPromptMessageRequest request) {
        RobotMessageRequest messageRequest = new RobotMessageRequest();
        if (StringUtils.isBlank(request.getRobotType())) {
            request.setRobotType(nbchatRobotConfigProperties.getRobotTypeDefault());
            try {
                String robotType = sysRobotConfigApi.getRobotValue(request.getTenantCode(), request.getUserId());
                request.setRobotType(robotType);
            } catch (Exception e) {
                log.error("获取机器人配置异常: {}", request, e);
            }
        }
        messageRequest.setRobotType(systemPresetPromptHelper.matchRobotType(request.getPresetId(), request.getBusiCode(), request.getRobotType()));
        messageRequest.setRobotTag(request.getRobotTag());
        messageRequest.setRequestId(request.getRequestId());
        messageRequest.setUserId(request.getUserId());
        messageRequest.setAppType(ChatAppType.TOOLS.getCode());
        messageRequest.setModelConfig(request.getModelConfig());
        return messageRequest;
    }

    @Override
    public Rsp getChatResult(RobotPromptMessageRequest request) {
        //生成requestId
        request.setRequestId(IdWorker.nextAutoIdStr());
        log.info("机器人消息处理[工具]: {}", request);
        long start = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(request.getPresetPrompts()) &&
            CollectionUtils.isEmpty(request.getMessages()) &&
            CollectionUtils.isEmpty(request.getPresetSysPrompts())) {
            return BaseRspUtils.createErrorRsp("请求参数异常!");
        }
        RobotMessageRequest messageRequest = buildMessageRequest(request);
        nbchatToolsListener.onRequest(request, messageRequest);
        ChatMessageBuilder messageBuilder = systemPresetPromptHelper.buildPromptMessage(request);
        if (messageBuilder.isSuccess()) {
            AiRobotChatRequest aiRobotChatRequest = AiRobotChatRequest.builder().
                    searchOnline(request.getSearchOnline()).
                    batchFlag(request.getBatchFlag()).
                    robotTag(request.getRobotTag()).
                    messageRequest(messageRequest).
                    messageBuilder(messageBuilder).build();
            RobotToolsChatResponse response = RobotToolsChatResponse.builder().
                    robotType(request.getRobotType()).
                    content("").
                    presetId(request.getPresetId()).build();
            try {
                ChatCompletionResult result = robotAiHelperFactory.chat(aiRobotChatRequest);
                log.info("大模型返回数据：{}", JSON.toJSONString(result));
                ChatCompletionChoice choice = result.getChoices().get(0);
                String res = choice.getMessage().getStrContent();
                if (request.isTrim()) {
                    res = JsonUtil.replaceAllBlank(res);
                }
                response.setContent(res);
                nbchatToolsListener.onResponse(request, response);
            } catch (Exception e) {
                log.info("大模型调用异常", e);
                response = nbchatToolsListener.onError(request, "模型调用异常", e);
            }
            long end = System.currentTimeMillis();
            log.info("机器人消息处理[工具]-调用完成: {}|{} ms", request.getRequestId(), (end - start));
            return BaseRspUtils.createSuccessRsp(response);
        } else {
            log.warn("机器人消息处理[工具]-构造提示词-参数异常: {}", request);
        }
        return BaseRspUtils.createErrorRsp("参数异常,请检查提示词配置!");
    }

    @Override
    public void sendMessage(RobotPromptMessageRequest request, RobotProcessCallback callback) {
        log.info("机器人消息处理[工具]: {}", request);
        RobotMessageRequest messageRequest = buildMessageRequest(request);
        ChatMessageBuilder messageBuilder;
        //根据 appType构建消息
        if (StringUtils.isNotEmpty(request.getAppType())) {
            messageRequest.setAppType(ChatAppType.DIC_CHAT.getCode());
            messageRequest.setSessionId(request.getSessionId());
            messageRequest.setText(request.getText());
            messageRequest.setRequestOptions(request.getRequestOptions());
            AppHelper appHelper = appHelperFactory.getAppHelper(request.getAppType());
            messageBuilder = appHelper.buildMessage(request);
        } else {
            messageBuilder = systemPresetPromptHelper.buildPromptMessage(request);
        }
        if (messageBuilder.isSuccess()) {
            try {
                nbchatMsgListener.onRequest(messageRequest);

                AiRobotChatRequest aiRobotChatRequest = AiRobotChatRequest.builder().
                        searchOnline(request.getSearchOnline()).
                        robotTag(request.getRobotTag()).
                        messageRequest(messageRequest).
                        messageBuilder(messageBuilder).build();
                robotAiHelperFactory.streamChat(aiRobotChatRequest).doOnError(err -> {
                    log.error("机器人消息处理[工具]-异常: {}", request, err);
                    RobotMsgContext errContext = nbchatMsgListener.onError(messageRequest, err);
                    callback.onMessage(errContext);
                }).blockingForEach(obj -> {
                    robotAiHelperFactory.convertChuckMessage(aiRobotChatRequest, obj);
                    RobotMsgContext context = nbchatMsgListener.onResponse(messageRequest, obj);
                    Optional.ofNullable(context).ifPresent(callback::onMessage);
                });
            } catch (SensitiveIncludeException e) {
                //敏感词警告
                log.warn("机器人消息处理[工具]-请求异常[触发敏感词]: {}", e.getMessage());
                RobotMsgContext warnContext = NbchatRobotMsgBuilder.buildSensitiveWaring(messageRequest, nbchatRobotConfigProperties);
                callback.onMessage(warnContext);
            } catch (NullPointerException e) {
                RobotMsgContext warnContext = NbchatRobotMsgBuilder.buildContext(request.getRequestId(),
                                                                                 "输入长度超过该模型限制，请减少输入内容或切换机器人在试试吧");
                callback.onMessage(warnContext);
            } catch (Exception e) {
                log.error("机器人消息处理[工具]-异常:{}", request, e);
                RobotMsgContext errContext = nbchatMsgListener.onError(messageRequest, e);
                callback.onMessage(errContext);
            }
        } else {
            RobotMsgContext warnContext = NbchatRobotMsgBuilder.buildContext(request.getRequestId(), "参数异常,请重新填写！");
            callback.onMessage(warnContext);
        }
    }

    @Override
    public Object getPPTChatResult(RobotPromptMessageRequest request) {
        //查询ppt描述，拼接到提示词中
        List<String> themeDescList = nbchatPresetPromptMapper.selectThemeDescs();
        //使用，分割集合
        String themeDescStr = String.join("; ", themeDescList);
        List<String> presetPrompts = request.getPresetPrompts();
        presetPrompts.add(themeDescStr);
        request.setPresetPrompts(presetPrompts);
        return getChatResult(request);
    }

}
