package com.tydic.nbchat.robot.core.openai;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionRequest;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionResult;
import com.tydic.nbchat.robot.api.openai.embedding.EmbeddingRequest;
import com.tydic.nbchat.robot.api.openai.embedding.EmbeddingResult;
import com.tydic.nbchat.robot.api.openai.embedding.EmbeddingSingleRequest;
import io.reactivex.Single;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.Streaming;

public interface OpenAiApi {

    //豆包智能体
    String DOUBAO_BATCH_API = "/api/v3/batch/chat/completions";
    String DOUBAO_BOTS_API = "/api/v3/bots/chat/completions";
    String DOUBAO_API = "/api/v3/chat/completions";
    String ZHIPU_API = "/api/paas/v4/chat/completions";
    String CHATGPT_API = "/v1/chat/completions";
    String BAIDU_API = "/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions";
    String BAIDU_PRO_API = "/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro";
    String BAIDU_TURBO_API = "/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_turbo";

    /** 接入文心一言 ***/
    @POST(BAIDU_API)
    Single<ChatCompletionResult> createBaiduChatCompletion(@Body ChatCompletionRequest request);

    @Streaming
    @POST(BAIDU_API)
    Call<ResponseBody> createBaiduChatCompletionStream(@Body ChatCompletionRequest request);

    //4.0
    @POST(BAIDU_PRO_API)
    Single<ChatCompletionResult> createBaiduChatCompletionPro(@Body ChatCompletionRequest request);

    //4.0
    @Streaming
    @POST(BAIDU_PRO_API)
    Call<ResponseBody> createBaiduChatCompletionStreamPro(@Body ChatCompletionRequest request);

    @POST(BAIDU_TURBO_API)
    Single<ChatCompletionResult> createBaiduChatCompletionTurbo(@Body ChatCompletionRequest request);

    @Streaming
    @POST(BAIDU_TURBO_API)
    Call<ResponseBody> createBaiduChatCompletionStreamTurbo(@Body ChatCompletionRequest request);
    /** 接入文心一言 ***/

    /** 接入chatgpt/fastchat规范 ***/
    @POST(CHATGPT_API)
    Single<JSONObject> createCommonChatCompletion(@Body ChatCompletionRequest request);

    @Streaming
    @POST(CHATGPT_API)
    Call<ResponseBody> createCommonChatCompletionStream(@Header("Accept") String accept,
                                                        @Body ChatCompletionRequest request);

    @POST(CHATGPT_API)
    Single<ChatCompletionResult> createChatCompletion(@Body ChatCompletionRequest request);

    @Streaming
    @POST(CHATGPT_API)
    Call<ResponseBody> createChatCompletionStream(@Body ChatCompletionRequest request);

    @POST("/v1/embeddings")
    Single<EmbeddingResult> createEmbeddings(@Body EmbeddingRequest request);

    @POST("/v1/embeddings")
    Single<EmbeddingResult> createEmbeddings(@Body EmbeddingSingleRequest request);

    /** 接入chatgpt/fastchat规范 ***/

    /** 接入智普ai规范 ***/

    @POST(ZHIPU_API)
    Single<ChatCompletionResult> createZPChatCompletion(@Body ChatCompletionRequest request);

    @Streaming
    @POST(ZHIPU_API)
    Call<ResponseBody> createZPChatCompletionStream(@Body ChatCompletionRequest request);

    /** 接入智普ai规范 ***/

    /** 接入豆包ai规范 ***/
    @POST(DOUBAO_API)
    Single<ChatCompletionResult> createDoubaoChatCompletion(@Body ChatCompletionRequest request);

    @Streaming
    @POST(DOUBAO_API)
    Call<ResponseBody> createDoubaoChatCompletionStream(@Body ChatCompletionRequest request);

    //智能体接口
    @POST(DOUBAO_BOTS_API)
    Single<ChatCompletionResult> createDoubaoBotsChatCompletion(@Body ChatCompletionRequest request);

    @Streaming
    @POST(DOUBAO_BOTS_API)
    Call<ResponseBody> createDoubaoBotsChatCompletionStream(@Body ChatCompletionRequest request);

    // 批量接口
    @POST(DOUBAO_BATCH_API)
    Single<ChatCompletionResult> createDoubaoBatchChatCompletion(@Body ChatCompletionRequest request);

    @Streaming
    @POST(DOUBAO_BATCH_API)
    Call<ResponseBody> createDoubaoBatchChatCompletionStream(@Body ChatCompletionRequest request);
    /** 接入豆包ai规范 ***/
}
