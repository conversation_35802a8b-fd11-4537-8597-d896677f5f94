package com.tydic.nbchat.pay.core.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.bo.file.FileUploadRequest;
import com.tydic.nbchat.admin.api.fileMannager.FileManageService;
import com.tydic.nbchat.pay.api.PayOpDataCountApi;
import com.tydic.nbchat.pay.api.bo.count.PayOrderCountBO;
import com.tydic.nbchat.pay.api.bo.count.PayOrderCountReqBO;
import com.tydic.nbchat.pay.api.bo.count.PayOrderSkuBO;
import com.tydic.nbchat.pay.api.bo.count.PayPriceCountBO;
import com.tydic.nbchat.pay.api.bo.count.PayPriceCountBO.PriceCount;
import com.tydic.nbchat.pay.api.bo.emus.PayBusiType;
import com.tydic.nbchat.pay.api.bo.emus.PayOrderStatus;
import com.tydic.nbchat.pay.api.bo.emus.PayType;
import com.tydic.nbchat.pay.core.handler.PayExcelSheetTitleHandler;
import com.tydic.nbchat.pay.mapper.PayGoodsSkuMapper;
import com.tydic.nbchat.pay.mapper.PayTradeRecordMapper;
import com.tydic.nbchat.pay.mapper.po.*;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.FileManagerHelper;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class PayOpDataCountImpl implements PayOpDataCountApi {

    @Resource
    private PayTradeRecordMapper payTradeRecordMapper;
    @Resource
    private PayGoodsSkuMapper payGoodsSkuMapper;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private FileManageService fileManageService;

    @Override
    public RspList<PayOrderCountBO> countPayDataList(PayOrderCountReqBO request) {
        log.info("统计支付订单列表: {}", request);
        PayOpDataSelectCondition cond = new PayOpDataSelectCondition();
        BeanUtils.copyProperties(request, cond);
        cond.setPhone(request.getPhone());
        cond.setPayStartTime(request.getPayStartTime());
        cond.setPayEndTime(request.getPayEndTime());
        cond.setSkuId(request.getSkuId());
        List<PayOrderCountBO> resultList = new ArrayList<>();
        Page<PayOrderCountBO> page = null;

        // 根据 isPaged 判断是否启用分页
        if (Boolean.TRUE.equals(request.getIsPaged())) {
            page = PageHelper.startPage(request.getPage(), request.getLimit());
        }
        if (request.getBusiType().equals(PayBusiType.ALL.getCode())) {
            List<PayOpDataSelectResult> recordList = payTradeRecordMapper.selectPayTradeRecord(cond);
            NiccCommonUtil.copyList(page != null ? page.getResult() : recordList, resultList, PayOrderCountBO.class);
        } else if (request.getBusiType().equals(PayBusiType.TDH.getCode())) {
            List<PayOpDataSelectResult> customizeRecordList = payTradeRecordMapper.selectPayTradeRecordByTdh(cond);
            NiccCommonUtil.copyList(page != null ? page.getResult() : customizeRecordList, resultList, PayOrderCountBO.class);
        } else if (request.getBusiType().equals(PayBusiType.VIP.getCode())) {
            List<PayOpDataSelectResult> vipList = payTradeRecordMapper.selectPayTradeRecordByVip(cond);
            NiccCommonUtil.copyList(page != null ? page.getResult() : vipList, resultList, PayOrderCountBO.class);
        } else {
            return BaseRspUtils.createSuccessRspList(resultList, 0L);
        }
        resultList.forEach(item -> {
            item.setRegChannel(JoinTenantType.getNameByCode(item.getRegChannel()));
            item.setPayType(PayType.getNameByCode(item.getPayType()));
        });
        return BaseRspUtils.createSuccessRspList(resultList, page != null ? page.getTotal() : resultList.size());
    }

    @Override
    public Rsp<PayPriceCountBO> countPayPrice(PayOrderCountReqBO request) {
        log.info("统计支付订单数据: {}", request);
        PayPriceCountBO payPriceCountBO = new PayPriceCountBO();
        PayPriceCountResult countResult = payTradeRecordMapper.countPayPrice();
        payPriceCountBO.setMonth(new PriceCount(countResult.getMonthlyTotalAmount(), countResult.getMonthlyPersonAmount(),
                                                countResult.getMonthlyPersonFirstAmount(), countResult.getMonthlyPersonRepeatAmount(),
                                                countResult.getMonthlyEnterpriseAmount()));
        payPriceCountBO.setWeek(new PriceCount(countResult.getWeeklyTotalAmount(), countResult.getWeeklyPersonAmount(),
                                               countResult.getWeeklyPersonFirstAmount(), countResult.getWeeklyPersonRepeatAmount(),
                                               countResult.getWeeklyEnterpriseAmount()));
        payPriceCountBO.setDay(new PriceCount(countResult.getDailyTotalAmount(), countResult.getDailyPersonAmount(),
                                              countResult.getDailyPersonFirstAmount(), countResult.getDailyPersonRepeatAmount(),
                                              countResult.getDailyEnterpriseAmount()));
        return BaseRspUtils.createSuccessRsp(payPriceCountBO);
    }

    @Override
    public RspList<PayOrderSkuBO> getSkuList(PayOrderCountReqBO request) {
        List<PayGoodsSku> list = payGoodsSkuMapper.selectByBusiType(request.getBusiType());
        List<PayOrderSkuBO> resultList = new ArrayList<>();
        for (PayGoodsSku sku : list) {
            PayOrderSkuBO bo = new PayOrderSkuBO();
            BeanUtils.copyProperties(sku, bo);
            resultList.add(bo);
        }
        return BaseRspUtils.createSuccessRspList(resultList);
    }

    @Override
    public Rsp exportPayDataList(PayOrderCountReqBO queryReqBO) {
        log.info("导出支付订单列表: {}", queryReqBO);
        queryReqBO.setPage(1);
        queryReqBO.setLimit(999999999);
        RspList<PayOrderCountBO> rspList = countPayDataList(queryReqBO);
        if (!rspList.isSuccess() || CollectionUtils.isEmpty(rspList.getRows())) {
            log.warn("查询订单统计: {}", rspList.getRspDesc());
            return BaseRspUtils.createErrorRsp("查询订单统计失败");
        }
        List<PayOpDataExportPo> list = new ArrayList<>();
        rspList.getRows().forEach(rsp -> {
            PayOpDataExportPo bo = new PayOpDataExportPo();
            BeanUtils.copyProperties(rsp, bo);
            bo.setOrderStatus(PayOrderStatus.getByCode(rsp.getOrderStatus()));
            bo.setIndex(list.size() + 1);
            list.add(bo);
        });
        String fileName = PayOpDataConstants.NO_SCORE_VALUE + new SimpleDateFormat("yyyy年MM月dd日HH时mm分ss秒").format(new Date()) + ".xlsx";
        String tempPath = System.getProperty("java.io.tmpdir");
        File dirFile = new File(tempPath + "/" + fileName);
        // 创建表头
        List<List<String>> head = createHead();
        // 注册表头处理器
        EasyExcel.write(dirFile)
                .head(head)
                .sheet(PayOpDataConstants.NO_SCORE_VALUE)
                .registerWriteHandler(new PayExcelSheetTitleHandler(PayOpDataConstants.SHEET1_COLUMN_NUMBER, PayOpDataConstants.TITLE_PAY_VALUE))
                .useDefaultStyle(true).relativeHeadRowIndex(1)
                .doWrite(list);
        // 获取最后一行的行号
        int rowCount = list.size();
        log.info("订单支付统计导出: 行数: {}", rowCount);
        //添加合并行
        double totalPayPrice = list.stream().mapToDouble(PayOpDataExportPo::getPayPrice).sum() / 100.00;
        String formattedTotalPayPriceStr = String.format("%.2f", totalPayPrice);
        double formattedTotalPayPrice = Double.parseDouble(formattedTotalPayPriceStr);
        addTotalRow(dirFile, rowCount, formattedTotalPayPrice);
        log.info("订单支付统计导出: {}|{}", dirFile.exists(), dirFile.getAbsolutePath());
        MultipartFile multipartFile = FileManagerHelper.parseToMultipartFile(dirFile);
        FileUploadRequest uploadRequest = new FileUploadRequest();
        uploadRequest.setTenantCode(queryReqBO.getTenantCode());
        uploadRequest.setFileName(dirFile.getName());
        uploadRequest.setUploadUser(queryReqBO.getBusiType());
        try {
            uploadRequest.setFile(multipartFile.getBytes());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        uploadRequest.setUseOriginalName(true);
        RspList fileManageSaveBOS = fileManageService.fileUploadRequest(uploadRequest);
        log.info("支付订单导出文件上传完毕，信息：{}", JSON.toJSONString(fileManageSaveBOS));
        if (!fileManageSaveBOS.isSuccess()) {
            log.error("文件上传失败: {}", fileManageSaveBOS.getRspDesc());
            return BaseRspUtils.createErrorRsp("文件上传失败");
        }
        return BaseRspUtils.createSuccessRsp(fileManageSaveBOS.getRows(), "上传成功");
    }

    /**
     * 创建动态表头
     */
    private static List<List<String>> createHead() {
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("序号"));
        head.add(Collections.singletonList("商品名称"));
        head.add(Collections.singletonList("支付金额"));
        head.add(Collections.singletonList("支付状态"));
        head.add(Collections.singletonList("支付时间"));
        head.add(Collections.singletonList("注册时间"));
        head.add(Collections.singletonList("渠道信息"));
        head.add(Collections.singletonList("支付方式"));
        head.add(Collections.singletonList("支付流水号"));
        head.add(Collections.singletonList("订单号"));
        head.add(Collections.singletonList("下单时间"));
        head.add(Collections.singletonList("用户名称"));
        head.add(Collections.singletonList("用户手机号"));
        head.add(Collections.singletonList("租户名称"));
        return head;
    }

    private void addTotalRow(File dirFile, int rowCount, double formattedTotalPayPrice) {
        Workbook workbook = null;
        try {
            FileInputStream fis = new FileInputStream(dirFile);
            workbook = WorkbookFactory.create(fis);
            fis.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 获取工作表
        Sheet sheet = workbook.getSheetAt(0);
        // 创建合计行
        Row totalRow = sheet.createRow(rowCount + 2);
        // 设置合计行的高度
        totalRow.setHeightInPoints(30);
        // 创建合计行的样式
        CellStyle totalStyle = workbook.createCellStyle();
        totalStyle.setAlignment(HorizontalAlignment.LEFT); // 改为靠左对齐
        // 创建合并区域
        CellRangeAddress cellRangeAddress = new CellRangeAddress(rowCount + 2, rowCount + 2, 0, 11); // 合并从第0列到第11列
        sheet.addMergedRegion(cellRangeAddress);
        Cell cell = totalRow.createCell(0);
        cell.setCellValue("累计支付金额: " + formattedTotalPayPrice);

        try {
            FileOutputStream fos = new FileOutputStream(dirFile);
            workbook.write(fos);
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
