# 首页数字人查询接口文档

## 接口概述
该接口用于查询首页数字人列表，查询tpl_type为"human"的模板数据，直接使用tpl_id作为tdhId查询对应的数字人信息。

## 接口信息
- **接口名称**: 首页数字人查询
- **请求方式**: POST
- **接口地址**: `/train/tdh/index/humans`
- **Content-Type**: application/json

## 请求参数

### 请求体 (JSON)
```json
{
    "page": 1,
    "limit": 10,
    "tenantCode": "string",
    "tplName": "string",
    "categoryCode": "string",
    "status": "1"
}
```

### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | Integer | 否 | 页码，默认1 |
| limit | Integer | 否 | 每页数量，默认10 |
| tenantCode | String | 否 | 租户编码 |
| tplName | String | 否 | 模板名称（模糊查询） |
| categoryCode | String | 否 | 分类编码 |
| status | String | 否 | 状态：0-未上架，1-已上架 |

## 响应参数

### 成功响应
```json
{
    "rspCode": "000000",
    "rspDesc": "业务处理成功!",
    "data": [
        {
            "tplId": "1234567890",
            "tenantCode": "tenant001",
            "tplName": "数字人模板1",
            "tplDesc": "这是一个数字人模板",
            "tplConfig": "{\"other\":\"config\"}",
            "tplContent": "{}",
            "tplType": "human",
            "orderIndex": 1,
            "createTime": "2024-01-01T10:00:00",
            "isValid": "1",
            "categoryCode": "category001",
            "tplSize": "16:9",
            "status": "1",
            "tdhId": "1234567890",
            "humanInfo": {
                "tdhId": "tdh001",
                "tenantCode": "tenant001",
                "userId": "user001",
                "tdhImg": "https://example.com/avatar.jpg",
                "tdhImgThu": "https://example.com/avatar_thumb.jpg",
                "tdhType": "2.5d_mtk",
                "tdhName": "数字人小明",
                "tdhTags": "[{\"name\":\"商务\",\"color\":\"#FF0000\"}]",
                "poseType": "2",
                "tdhSource": "0",
                "createTime": "2024-01-01T09:00:00",
                "orderIndex": 1,
                "isValid": "1",
                "vipFlag": "0",
                "gender": "男",
                "targetVoice": "voice001"
            }
        }
    ],
    "total": 1
}
```

### 失败响应
```json
{
    "rspCode": "999999",
    "rspDesc": "查询首页数字人列表失败：具体错误信息",
    "data": null
}
```

### 响应字段说明

#### 模板信息字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| tplId | String | 模板主键 |
| tenantCode | String | 租户编码 |
| tplName | String | 模板名称 |
| tplDesc | String | 模板描述 |
| tplConfig | String | 模板配置JSON |
| tplContent | String | 模板内容JSON |
| tplType | String | 模板类型，固定为"human" |
| orderIndex | Integer | 排序索引 |
| createTime | String | 创建时间 |
| isValid | String | 是否有效：0-无效，1-有效 |
| categoryCode | String | 分类编码 |
| tplSize | String | 模板尺寸 |
| status | String | 上架状态：0-未上架，1-已上架 |
| tdhId | String | 数字人ID（等于tpl_id） |

#### 数字人信息字段 (humanInfo)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| tdhId | String | 数字人ID |
| tenantCode | String | 租户编码 |
| userId | String | 用户ID |
| tdhImg | String | 数字人图片地址 |
| tdhImgThu | String | 数字人缩略图地址 |
| tdhType | String | 数字人类型：2d_gif/2.5d_mtk/2d_mtk等 |
| tdhName | String | 数字人名称 |
| tdhTags | String | 数字人标签JSON |
| poseType | String | 姿态类型：1-全身，2-半身，3-大半身，4-坐姿 |
| tdhSource | String | 来源：0-系统内置，1-自定义 |
| createTime | String | 创建时间 |
| orderIndex | Integer | 排序索引 |
| isValid | String | 是否有效：0-无效，1-有效 |
| vipFlag | String | VIP标识：0-普通会员，其他值对应VIP类型 |
| gender | String | 性别：男/女 |
| targetVoice | String | 绑定音色ID |

## Postman 配置示例

### 1. 基本配置
- **Method**: POST
- **URL**: `{{baseUrl}}/train/tdh/index/humans`
- **Headers**:
  - Content-Type: application/json
  - Authorization: Bearer {{token}} (如果需要)

### 2. 请求体示例

#### 查询所有数字人模板
```json
{
    "page": 1,
    "limit": 10
}
```

#### 按租户查询
```json
{
    "page": 1,
    "limit": 10,
    "tenantCode": "tenant001"
}
```

#### 按模板名称模糊查询
```json
{
    "page": 1,
    "limit": 10,
    "tplName": "数字人"
}
```

#### 查询已上架的模板
```json
{
    "page": 1,
    "limit": 10,
    "status": "1"
}
```

### 3. 环境变量
在Postman中设置以下环境变量：
- `baseUrl`: 服务器基础URL，如 `http://localhost:8080`
- `token`: 认证令牌（如果需要）

## 业务逻辑说明

1. **模板筛选**: 接口只查询 `tpl_type` 为 "human" 的模板
2. **状态过滤**: 只返回 `isValid` 为 "1"（有效）且 `status` 为 "1"（已上架）的模板
3. **数字人关联**: 直接使用 `tpl_id` 作为 `tdhId` 查询对应的数字人信息
4. **批量查询**: 使用批量查询优化性能，避免N+1查询问题
5. **分页支持**: 支持分页查询，返回总数和当前页数据

## 错误码说明
- `000000`: 成功
- `999999`: 系统错误
- 其他错误码根据具体业务场景定义

## 注意事项
1. `tdhId` 字段直接等于模板的 `tpl_id`
2. 如果根据 `tdhId` 查询不到数字人信息或数字人已失效，则 `humanInfo` 为 null
3. 接口会自动过滤掉无效的模板和数字人数据
4. 使用批量查询优化性能，避免循环调用数据库
5. 建议在生产环境中添加适当的权限控制和访问限制
