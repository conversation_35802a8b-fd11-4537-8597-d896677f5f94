package com.tydic.nbchat.train.report.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.NbchatTrainRpVideoService;
import com.tydic.nbchat.train.api.bo.constants.RedisConstants;
import com.tydic.nbchat.train.api.bo.eums.TaskStateType;
import com.tydic.nbchat.train.api.bo.video.NbchatVideoCountQueryRspBO;
import com.tydic.nbchat.train.api.bo.video.NbchatVideoQueryReqBO;
import com.tydic.nbchat.train.api.bo.video.NbchatVideoQueryRspBO;
import com.tydic.nbchat.train.core.busi.TdhQueueCountBusiService;
import com.tydic.nbchat.train.mapper.TdhCreationTaskMapper;
import com.tydic.nbchat.train.mapper.po.NbchatVideoCondition;
import com.tydic.nbchat.train.mapper.po.NbchatVideoDTO;
import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.bo.eums.UserTypeEnum;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
public class NbchatTrainRpVideoServiceImpl implements NbchatTrainRpVideoService {

    @Resource
    private TdhCreationTaskMapper tdhCreationTaskMapper;

    @Resource
    private RedisHelper redisHelper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}", group = "${nicc-dc-config.dubbo-provider.group}", timeout = 10000)
    private NbchatUserApi nbchatUserApi;

    private final TdhQueueCountBusiService tdhQueueCountBusiService;

    public NbchatTrainRpVideoServiceImpl(TdhQueueCountBusiService tdhQueueCountBusiService) {
        this.tdhQueueCountBusiService = tdhQueueCountBusiService;
    }

    @Override
    public Rsp getVideoCount(NbchatVideoQueryReqBO reqBO) {
        log.info("查询视频制作数量, 请求用户ID: {}", reqBO.get_userId());

        NbchatVideoCountQueryRspBO rspBO = new NbchatVideoCountQueryRspBO();
        Map<String, Integer> statsMap = new HashMap<>();

        try {
            // 先尝试从Redis获取历史统计数据
            if (redisHelper.hasKey(RedisConstants.TDH_VIDEO_COUNT)) {
                log.debug("从Redis缓存获取视频统计数据");
                Map<String, Object> videoMap = (Map<String, Object>) redisHelper.get(RedisConstants.TDH_VIDEO_COUNT);
                // 将数据放入我们的统计Map
                for (Map.Entry<String, Object> entry : videoMap.entrySet()) {
                    statsMap.put(entry.getKey(), (Integer) entry.getValue());
                }
            } else {
                log.debug("Redis中无缓存数据，从数据库获取视频统计数据");
                // 查询历史数据并存入Redis
                statsMap.put("lastWeek", tdhCreationTaskMapper.getVideoCount(6, null));
                statsMap.put("lastMonth", tdhCreationTaskMapper.getVideoCount(29, null));
                statsMap.put("enterpriseLastWeek", tdhCreationTaskMapper.getVideoCount(6, UserTypeEnum.COMPANY_USER.getCode()));
                statsMap.put("enterpriseLastMonth", tdhCreationTaskMapper.getVideoCount(29, UserTypeEnum.COMPANY_USER.getCode()));
                statsMap.put("personalLastWeek", tdhCreationTaskMapper.getVideoCount(6, UserTypeEnum.COMMON_USER.getCode()));
                statsMap.put("personalLastMonth", tdhCreationTaskMapper.getVideoCount(29, UserTypeEnum.COMMON_USER.getCode()));

                // 计算Redis过期时间为当天结束
                LocalDateTime endOfDay = LocalDate.now().atTime(23, 59, 59);
                long seconds = Duration.between(LocalDateTime.now(), endOfDay).getSeconds();
                seconds = Math.max(seconds, 60); // 确保至少60秒的有效期

                // 缓存到Redis
                redisHelper.set(RedisConstants.TDH_VIDEO_COUNT, statsMap, seconds);
            }

            // 获取当天数据（始终从数据库获取实时数据）
            int today = tdhCreationTaskMapper.getVideoCount(0, null);
            int enterpriseToday = tdhCreationTaskMapper.getVideoCount(0, UserTypeEnum.COMPANY_USER.getCode());
            int personalToday = tdhCreationTaskMapper.getVideoCount(0, UserTypeEnum.COMMON_USER.getCode());

            // 填充返回结果
            rspBO.setToday(today);
            rspBO.setLastWeek(statsMap.getOrDefault("lastWeek", 0) + today);
            rspBO.setLastMonth(statsMap.getOrDefault("lastMonth", 0) + today);

            rspBO.setEnterpriseToday(enterpriseToday);
            rspBO.setEnterpriseLastWeek(statsMap.getOrDefault("enterpriseLastWeek", 0) + enterpriseToday);
            rspBO.setEnterpriseLastMonth(statsMap.getOrDefault("enterpriseLastMonth", 0) + enterpriseToday);

            rspBO.setPersonalToday(personalToday);
            rspBO.setPersonalLastWeek(statsMap.getOrDefault("personalLastWeek", 0) + personalToday);
            rspBO.setPersonalLastMonth(statsMap.getOrDefault("personalLastMonth", 0) + personalToday);

            log.debug("视频统计数据查询完成: {}", rspBO);
            return BaseRspUtils.createSuccessRsp(rspBO);
        } catch (Exception e) {
            log.error("获取视频统计数据异常", e);
            return BaseRspUtils.createSuccessRsp(new NbchatVideoCountQueryRspBO()); // 返回空统计，避免前端异常
        }
    }

    @Override
    public RspList getVideoList(NbchatVideoQueryReqBO reqBO) {
        log.info("查询视频列表:{}",reqBO);
        List<NbchatVideoQueryRspBO> rspBOList = new ArrayList<>();
        NbchatVideoCondition condition = new NbchatVideoCondition();
        BeanUtils.copyProperties(reqBO, condition);
        Page page = null;
        if (Boolean.TRUE.equals(reqBO.getIsPaged())) {
            page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        }
        if (condition.getTaskStateList() != null &&
                (condition.getTaskStateList().contains(TaskStateType.GENERATING.getCode())
                || condition.getTaskStateList().contains(TaskStateType.IN_THE_LINE.getCode()))) {
            //不展示已删除的任务
            condition.setIsValid(EntityValidType.NORMAL.getCode());
        }
         List<NbchatVideoDTO> videoList = tdhCreationTaskMapper.getVideoList(condition);
        if (CollectionUtils.isEmpty(videoList)) {
            return BaseRspUtils.createSuccessRspList(rspBOList,0L);
        }
        NiccCommonUtil.copyList(videoList,rspBOList,NbchatVideoQueryRspBO.class);
        //获取排队任务
        Map<String,Integer> queueMap = tdhQueueCountBusiService.getTaskQueueMap();
        for (NbchatVideoQueryRspBO bo : rspBOList) {
            if (ObjectUtils.allNotNull(bo.getStartTime(),bo.getEndTime())) {
                bo.setDuration((bo.getEndTime().getTime() - bo.getStartTime().getTime()) / 1000);
            }
            if (ObjectUtils.isEmpty(bo.getEndTime())) {
                bo.setDuration((new Date().getTime() - bo.getStartTime().getTime()) / 1000);
            }
            if (queueMap.containsKey(bo.getTaskId())) {
                bo.setQueueNum(queueMap.get(bo.getTaskId()));
            }
        }
        return BaseRspUtils.createSuccessRspList(rspBOList,page != null ? page.getTotal() : videoList.size());
    }
}
