package com.tydic.nbchat.train.api.bo.ppt;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class NbchatPptQueryRspBO implements Serializable {

    private String vipType;      // 视频vip类型  1体验会员 2高级会员 3专业会员 4专业会员-Max
    private String vipTypeSub;   // 视频vip类型子类: 0=临时卡，1=月卡，2=季卡，3=年卡
    private Integer vipUseDays;  // 开通会员时间-创作时间
    private Integer registerDays;// 注册时间-创作时间

    private String pptType; //0:普通ppt 1:美化ppt
    private String layout;  //布局列表

    private String fileUrl;
    private String fileId;
    private String fileVector;

    /**
     * 主键
     */
    private String pptId;

    private String tenantCode;
    /**
     * 用户
     */
    private String userId;
    /**
     * 关联主题
     */
    private String themeId;
    /**
     * 创作名称
     */
    private String creationName;
    /**
     * 文档标题:json
     */
    private String titleContent;
    //创作方式 1-AI生成PPT 2-上传文档转PPT 3-资料PPT
    private String creationType;
    /**
     * ppt页数
     */
    private Integer partCount;
    /**
     * ai解析内容
     */
    private String aiContent;
    /**
     * 预览图
     */
    private String previewUrl;
    /**
     * 是否有效 0 否 1 是
     */
    private String isValid;
    /**
     * 是否共享 0 否 1 是
     */
    private String isShare;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date updateTime;
    /**
     * 创作者名称
     */
    private String userName;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 付费状态
     */
    private String vipStatus;
    /**
     * 用户属性
     */
    private String userType;
    /**
     * 企业名称
     */
    private String companyName;
}
