package com.tydic.nbchat.user.api.bo.vip;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户vip充值任务
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserVipOrderContext implements Serializable {
    //订单id
    @ParamNotEmpty
    private String orderNo;
    @ParamNotEmpty
    private String tradeNo;
    @ParamNotNull
    private Date orderTime;
    @ParamNotEmpty
    private String tenantCode;
    @ParamNotEmpty
    private String userId;
    //充值计划
    @ParamNotNull
    private Integer score;
    @ParamNotNull
    private Integer days;
    @ParamNotNull
    private Integer cycle;
    //会员类型 0 加油包 1 体验会员 2 高级会员 3 专业会员 4 专业会员Max
    @ParamNotEmpty
    private String vipType;
    /**
     * 支付金额/分
     */
    private Integer payPrice;
}
