package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexHumanQueryRspBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateDeleteReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateQueryRspBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateSortReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateStatusReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualHumanQueryRspBO;
import com.tydic.nbchat.train.api.tdh.TdhIndexTemplateApi;
import com.tydic.nbchat.train.mapper.TdhIndexTemplateMapper;
import com.tydic.nbchat.train.mapper.TdhVirtualHumanMapper;
import com.tydic.nbchat.train.mapper.po.TdhIndexTemplate;
import com.tydic.nbchat.train.mapper.po.TdhVirtualHuman;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@AllArgsConstructor
public class TdhIndexTemplateServiceImpl implements TdhIndexTemplateApi {
    private final TdhIndexTemplateMapper tdhIndexTemplateMapper;
    private final TdhVirtualHumanMapper tdhVirtualHumanMapper;

    @Override
    public RspList<TdhIndexTemplateQueryRspBO> list(TdhIndexTemplateQueryReqBO request) {
        List<TdhIndexTemplateQueryRspBO> rspBOList = new ArrayList<>();
        TdhIndexTemplate tdhIndexTemplate = new TdhIndexTemplate();
        BeanUtils.copyProperties(request, tdhIndexTemplate);
        tdhIndexTemplate.setTenantCode(null);
        tdhIndexTemplate.setIsValid(EntityValidType.NORMAL.getCode());
        Page<TdhIndexTemplate> page = PageHelper.startPage(request.getPage(), request.getLimit());
        tdhIndexTemplateMapper.selectList(tdhIndexTemplate);
        if (CollectionUtils.isEmpty(page.getResult())) {
           return BaseRspUtils.createSuccessRspList(rspBOList,0L);
        }
        NiccCommonUtil.copyList(page.getResult(), rspBOList, TdhIndexTemplateQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspBOList, page.getTotal());
    }

    @Override
    public Rsp info(TdhIndexTemplateQueryReqBO reqBO) {
        TdhIndexTemplate tdhIndexTemplate = tdhIndexTemplateMapper.selectByPrimaryKey(reqBO.getTplId());
        if (tdhIndexTemplate != null) {
            TdhIndexTemplateQueryRspBO rspBO = new TdhIndexTemplateQueryRspBO();
            BeanUtils.copyProperties(tdhIndexTemplate, rspBO);
            return BaseRspUtils.createSuccessRsp(rspBO,"查询成功");
        }
        return BaseRspUtils.createErrorRsp("查询失败-没有找到对应的模板");
    }

    @Override
    public RspList<TdhIndexTemplateQueryRspBO> adminList(TdhIndexTemplateQueryReqBO request) {
        List<TdhIndexTemplateQueryRspBO> rspBOList = new ArrayList<>();
        TdhIndexTemplate tdhIndexTemplate = new TdhIndexTemplate();
        BeanUtils.copyProperties(request, tdhIndexTemplate);
        // 管理员查询不限制租户，可以查询所有模板
        tdhIndexTemplate.setTenantCode(null);
        // 管理员查询可以查询所有状态的模板，包括无效的
        if (StringUtils.isBlank(request.getIsValid())) {
            tdhIndexTemplate.setIsValid(null);
        }
        Page<TdhIndexTemplate> page = PageHelper.startPage(request.getPage(), request.getLimit());
        tdhIndexTemplateMapper.selectList(tdhIndexTemplate);
        if (CollectionUtils.isEmpty(page.getResult())) {
           return BaseRspUtils.createSuccessRspList(rspBOList,0L);
        }
        NiccCommonUtil.copyList(page.getResult(), rspBOList, TdhIndexTemplateQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspBOList, page.getTotal());
    }

    @Override
    public Rsp addTemplate(TdhIndexTemplateQueryReqBO reqBO) {
        try {
            // 参数校验
            if (StringUtils.isBlank(reqBO.getTplName())) {
                return BaseRspUtils.createErrorRsp("模板名称不能为空");
            }
            if (StringUtils.isBlank(reqBO.getTplType())) {
                return BaseRspUtils.createErrorRsp("模板类型不能为空");
            }

            TdhIndexTemplate template = new TdhIndexTemplate();
            BeanUtils.copyProperties(reqBO, template);

            // 设置默认值
            template.setTplId(StringUtils.isNotBlank(reqBO.getTplId())?reqBO.getTplId():IdWorker.nextAutoIdStr());
            template.setCreateTime(new Date());
            template.setIsValid(EntityValidType.NORMAL.getCode());
            if (StringUtils.isBlank(template.getStatus())) {
                template.setStatus("0"); // 默认未上架
            }
            if (template.getOrderIndex() == null) {
                template.setOrderIndex(0);
            }

            // 处理JSON字段，确保是有效的JSON格式
            template.setTplConfig(validateAndFormatJson(template.getTplConfig(), "{}"));
            template.setTplContent(validateAndFormatJson(template.getTplContent(), "{}"));

            int result = tdhIndexTemplateMapper.insertSelective(template);
            if (result > 0) {
                return BaseRspUtils.createSuccessRsp(template.getTplId(), "新增模板成功");
            } else {
                return BaseRspUtils.createErrorRsp("新增模板失败");
            }
        } catch (Exception e) {
            log.error("新增首页模板失败", e);
            return BaseRspUtils.createErrorRsp("新增模板失败：" + e.getMessage());
        }
    }

    @Override
    public Rsp updateTemplate(TdhIndexTemplateQueryReqBO reqBO) {
        try {
            // 参数校验
            if (StringUtils.isBlank(reqBO.getTplId())) {
                return BaseRspUtils.createErrorRsp("模板ID不能为空");
            }

            // 检查模板是否存在
            TdhIndexTemplate existTemplate = tdhIndexTemplateMapper.selectByPrimaryKey(reqBO.getTplId());
            if (existTemplate == null) {
                return BaseRspUtils.createErrorRsp("模板不存在");
            }

            TdhIndexTemplate template = new TdhIndexTemplate();
            BeanUtils.copyProperties(reqBO, template);

            // 处理JSON字段，确保是有效的JSON格式
            if (StringUtils.isNotBlank(template.getTplConfig())) {
                template.setTplConfig(validateAndFormatJson(template.getTplConfig(), "{}"));
            }
            if (StringUtils.isNotBlank(template.getTplContent())) {
                template.setTplContent(validateAndFormatJson(template.getTplContent(), "{}"));
            }

            int result = tdhIndexTemplateMapper.updateByPrimaryKeySelective(template);
            if (result > 0) {
                return BaseRspUtils.createSuccessRsp(template.getTplId(), "更新模板成功");
            } else {
                return BaseRspUtils.createErrorRsp("更新模板失败");
            }
        } catch (Exception e) {
            log.error("更新首页模板失败", e);
            return BaseRspUtils.createErrorRsp("更新模板失败：" + e.getMessage());
        }
    }

    @Override
    public Rsp deleteTemplate(TdhIndexTemplateDeleteReqBO reqBO) {
        try {
            // 参数校验
            if (reqBO == null || StringUtils.isBlank(reqBO.getTplId())) {
                return BaseRspUtils.createErrorRsp("模板ID不能为空");
            }

            String tplId = reqBO.getTplId();
            // 检查模板是否存在
            TdhIndexTemplate existTemplate = tdhIndexTemplateMapper.selectByPrimaryKey(tplId);
            if (existTemplate == null) {
                return BaseRspUtils.createErrorRsp("模板不存在");
            }

            // 逻辑删除，设置为无效状态
            TdhIndexTemplate template = new TdhIndexTemplate();
            template.setTplId(tplId);
            template.setIsValid(EntityValidType.DELETE.getCode());

            int result = tdhIndexTemplateMapper.updateByPrimaryKeySelective(template);
            if (result > 0) {
                return BaseRspUtils.createSuccessRsp(tplId, "删除模板成功");
            } else {
                return BaseRspUtils.createErrorRsp("删除模板失败");
            }
        } catch (Exception e) {
            log.error("删除首页模板失败", e);
            return BaseRspUtils.createErrorRsp("删除模板失败：" + e.getMessage());
        }
    }

    @Override
    public Rsp sortTemplates(TdhIndexTemplateSortReqBO reqBO) {
        try {
            // 参数校验
            if (reqBO == null || CollectionUtils.isEmpty(reqBO.getTplIds())) {
                return BaseRspUtils.createErrorRsp("模板ID列表不能为空");
            }

            List<String> tplIds = reqBO.getTplIds();
            List<TdhIndexTemplate> updateList = new ArrayList<>();
            for (int i = 0; i < tplIds.size(); i++) {
                String tplId = tplIds.get(i);
                if (StringUtils.isBlank(tplId)) {
                    continue;
                }

                // 检查模板是否存在
                TdhIndexTemplate existTemplate = tdhIndexTemplateMapper.selectByPrimaryKey(tplId);
                if (existTemplate == null) {
                    return BaseRspUtils.createErrorRsp("模板不存在：" + tplId);
                }

                TdhIndexTemplate template = new TdhIndexTemplate();
                template.setTplId(tplId);
                template.setOrderIndex(i + 1); // 排序从1开始
                updateList.add(template);
            }

            if (!updateList.isEmpty()) {
                int result = tdhIndexTemplateMapper.updateBatchSelective(updateList);
                if (result > 0) {
                    return BaseRspUtils.createSuccessRsp(result, "模板排序成功");
                } else {
                    return BaseRspUtils.createErrorRsp("模板排序失败");
                }
            } else {
                return BaseRspUtils.createErrorRsp("没有有效的模板ID");
            }
        } catch (Exception e) {
            log.error("首页模板排序失败", e);
            return BaseRspUtils.createErrorRsp("模板排序失败：" + e.getMessage());
        }
    }

    @Override
    public Rsp updateTemplateStatus(TdhIndexTemplateStatusReqBO reqBO) {
        try {
            // 参数校验
            if (reqBO == null || StringUtils.isBlank(reqBO.getTplId())) {
                return BaseRspUtils.createErrorRsp("模板ID不能为空");
            }
            if (StringUtils.isBlank(reqBO.getStatus())) {
                return BaseRspUtils.createErrorRsp("状态不能为空");
            }

            String tplId = reqBO.getTplId();
            String status = reqBO.getStatus();

            if (!StateEnum.STATE.UNAVAILABLE.getCode().equals(status) && !StateEnum.STATE.AVAILABLE.getCode().equals(status)) {
                return BaseRspUtils.createErrorRsp("状态值无效，只能是0（下架）或1（上架）");
            }

            // 检查模板是否存在
            TdhIndexTemplate existTemplate = tdhIndexTemplateMapper.selectByPrimaryKey(tplId);
            if (existTemplate == null) {
                return BaseRspUtils.createErrorRsp("模板不存在");
            }

            // 检查模板是否已删除
            if (EntityValidType.DELETE.getCode().equals(existTemplate.getIsValid())) {
                return BaseRspUtils.createErrorRsp("模板已删除，无法修改状态");
            }

            // 检查状态是否已经是目标状态
            if (status.equals(existTemplate.getStatus())) {
                String statusDesc = StateEnum.STATE.AVAILABLE.getCode().equals(status) ? "上架" : "下架";
                return BaseRspUtils.createErrorRsp("模板已经是" + statusDesc + "状态");
            }

            TdhIndexTemplate template = new TdhIndexTemplate();
            template.setTplId(tplId);
            template.setStatus(status);

            int result = tdhIndexTemplateMapper.updateByPrimaryKeySelective(template);
            if (result > 0) {
                String operation = StateEnum.STATE.AVAILABLE.getCode().equals(status) ? "上架" : "下架";
                return BaseRspUtils.createSuccessRsp(tplId, "模板" + operation + "成功");
            } else {
                return BaseRspUtils.createErrorRsp("模板状态更新失败");
            }
        } catch (Exception e) {
            log.error("首页模板上下架失败", e);
            return BaseRspUtils.createErrorRsp("模板状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 验证和格式化JSON字符串
     * @param jsonStr 输入的JSON字符串
     * @param defaultValue 默认值
     * @return 有效的JSON字符串
     */
    private String validateAndFormatJson(String jsonStr, String defaultValue) {
        if (StringUtils.isBlank(jsonStr)) {
            return defaultValue;
        }

        // 简单的JSON格式验证
        jsonStr = jsonStr.trim();
        if (!jsonStr.startsWith("{") && !jsonStr.startsWith("[")) {
            // 如果不是JSON格式，返回默认值
            log.warn("输入的字符串不是有效的JSON格式: {}", jsonStr);
            return defaultValue;
        }

        try {
            // 这里可以添加更严格的JSON验证，比如使用Jackson或Gson
            // 暂时只做基本的格式检查
            if (jsonStr.startsWith("{") && !jsonStr.endsWith("}")) {
                return defaultValue;
            }
            if (jsonStr.startsWith("[") && !jsonStr.endsWith("]")) {
                return defaultValue;
            }
            return jsonStr;
        } catch (Exception e) {
            log.warn("JSON格式验证失败: {}", jsonStr, e);
            return defaultValue;
        }
    }

    @Override
    public RspList<TdhIndexHumanQueryRspBO> getIndexHumans(TdhIndexTemplateQueryReqBO request) {
        try {
            List<TdhIndexHumanQueryRspBO> rspBOList = new ArrayList<>();

            // 构建查询条件，只查询tpl_type为human的模板
            TdhIndexTemplate queryCondition = new TdhIndexTemplate();
            BeanUtils.copyProperties(request, queryCondition);
            queryCondition.setTplType("human");
            queryCondition.setIsValid(EntityValidType.NORMAL.getCode());
            queryCondition.setStatus(StateEnum.STATE.AVAILABLE.getCode()); // 只查询已上架的模板

            // 分页查询
            Page<TdhIndexTemplate> page = PageHelper.startPage(request.getPage(), request.getLimit());
            tdhIndexTemplateMapper.selectList(queryCondition);

            if (CollectionUtils.isEmpty(page.getResult())) {
                return BaseRspUtils.createSuccessRspList(rspBOList, 0L);
            }

            // 收集所有模板ID用于批量查询数字人信息
            List<String> tplIds = new ArrayList<>();
            for (TdhIndexTemplate template : page.getResult()) {
                tplIds.add(template.getTplId());
            }

            // 批量查询数字人信息
            List<TdhVirtualHuman> humanList = new ArrayList<>();
            if (!tplIds.isEmpty()) {
                humanList = tdhVirtualHumanMapper.selectByTplIds(tplIds);
            }

            // 构建tplId到数字人的映射
            Map<String, TdhVirtualHuman> humanMap = new HashMap<>();
            for (TdhVirtualHuman human : humanList) {
                if (human != null && EntityValidType.NORMAL.getCode().equals(human.getIsValid())) {
                    humanMap.put(human.getTdhId(), human);
                }
            }

            // 处理每个模板，关联数字人信息
            for (TdhIndexTemplate template : page.getResult()) {
                TdhIndexHumanQueryRspBO rspBO = new TdhIndexHumanQueryRspBO();
                BeanUtils.copyProperties(template, rspBO);

                // 直接使用tpl_id作为tdhId
                String tdhId = template.getTplId();
                rspBO.setTdhId(tdhId);

                // 从映射中获取数字人信息
                TdhVirtualHuman human = humanMap.get(tdhId);
                if (human != null) {
                    TdhVirtualHumanQueryRspBO humanInfo = new TdhVirtualHumanQueryRspBO();
                    BeanUtils.copyProperties(human, humanInfo);
                    rspBO.setHumanInfo(humanInfo);
                }

                rspBOList.add(rspBO);
            }

            return BaseRspUtils.createSuccessRspList(rspBOList, page.getTotal());

        } catch (Exception e) {
            log.error("查询首页数字人列表失败", e);
            return BaseRspUtils.createErrorRspList("查询首页数字人列表失败：" + e.getMessage());
        }
    }

}
