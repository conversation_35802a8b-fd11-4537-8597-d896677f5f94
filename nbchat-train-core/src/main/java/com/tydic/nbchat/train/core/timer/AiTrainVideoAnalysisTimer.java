package com.tydic.nbchat.train.core.timer;

import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.robot.api.NbchatRobotToolsApi;
import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.api.bo.tools.RobotToolsChatResponse;
import com.tydic.nbchat.train.mapper.TdhCreationTaskMapper;
import com.tydic.nbchat.train.mapper.po.TdhAnalysisDTO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.entity.RedisLockEntity;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 16:10
 * @description 视频分析
 */
@Slf4j
@Component
@EnableScheduling
public class AiTrainVideoAnalysisTimer {

    private final static String NBCHAT_TRAIN_VIDEO_ANALYSIS_LOCK_KEY = "NBCHAT_TRAIN_VIDEO_ANALYSIS_LOCK";

    @Resource
    private RedisHelper redisHelper;
    @Resource
    private TdhCreationTaskMapper tdhCreationTaskMapper;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}", group = "${nicc-dc-config.dubbo-provider.group}", timeout = 120000)
    private NbchatRobotToolsApi nbchatRobotToolsApi;

    //    @Scheduled(cron = "0 0/1 * * * ?")
    public void run() {
        //加锁
        RedisLockEntity redisLockEntity = RedisLockEntity.builder().lockKey(NBCHAT_TRAIN_VIDEO_ANALYSIS_LOCK_KEY).requestId(IdWorker.nextAutoIdStr()).build();
        boolean locked = redisHelper.lock(redisLockEntity);
        try {
            log.info("视频分析任务,开始执行:{}-{}", redisLockEntity, locked);
            if (locked) {
                List<TdhAnalysisDTO> tdhAnalysisList = tdhCreationTaskMapper.selectTdhForAnalysis();
                for (TdhAnalysisDTO tdhAnalysis : tdhAnalysisList) {
                    Rsp<RobotToolsChatResponse> chatResult = chat(tdhAnalysis.getTaskId(), tdhAnalysis.getOralDraft());
                    log.info("调用大模型数据-返回:{}", JSON.toJSONString(chatResult));
                    if (chatResult.isSuccess()) {
                        String content = chatResult.getData().getContent();
                        // TODO
                    }
                }
            }
            log.info("视频分析任务,执行完成:{}", redisLockEntity);
        } catch (Exception e) {
            log.error("视频分析任务,执行异常:{}", redisLockEntity, e);
        } finally {
            if (locked) {
                redisHelper.unlockLua(redisLockEntity);
            }
        }
    }

    private Rsp<RobotToolsChatResponse> chat(String userId, String oralDraft) {
        RobotPromptMessageRequest request = new RobotPromptMessageRequest();
        request.setUserId(userId);
        request.setRobotType("doubao_video_class");
        request.setRobotTag("video_class");
        request.setPresetId("video_class");
        request.setPresetPrompts(Collections.singletonList(oralDraft));
        request.setSearchOnline(false);
        request.setBatchFlag(true);
        request.setTrim(true);

        log.info("调用大模型数据-请求:{}", JSON.toJSONString(request));
        return nbchatRobotToolsApi.getChatResult(request);
    }
}
