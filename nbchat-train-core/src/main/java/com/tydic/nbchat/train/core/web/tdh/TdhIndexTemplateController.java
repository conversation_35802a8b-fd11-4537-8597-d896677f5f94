package com.tydic.nbchat.train.core.web.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhIndexHumanQueryRspBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateDeleteReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateQueryRspBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateSortReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateStatusReqBO;
import com.tydic.nbchat.train.api.tdh.TdhIndexTemplateApi;
import com.tydic.nicc.common.bo.premission.Logical;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/train/tdh")
public class TdhIndexTemplateController {

    private final TdhIndexTemplateApi tdhIndexTemplateApi;

    public TdhIndexTemplateController(TdhIndexTemplateApi tdhIndexTemplateApi) {
        this.tdhIndexTemplateApi = tdhIndexTemplateApi;
    }

    @PostMapping("/index/templates")
    public RspList<TdhIndexTemplateQueryRspBO> list(@RequestBody TdhIndexTemplateQueryReqBO request) {
        return tdhIndexTemplateApi.list(request);
    }

    @PostMapping("/index/template/info")
    public Rsp info(@RequestBody TdhIndexTemplateQueryReqBO reqBO) {
        return tdhIndexTemplateApi.info(reqBO);
    }

    /**
     * 查询首页数字人列表
     */
    @PostMapping("/index/humans")
    public RspList<TdhIndexHumanQueryRspBO> getIndexHumans(@RequestBody TdhIndexTemplateQueryReqBO request) {
        return tdhIndexTemplateApi.getIndexHumans(request);
    }

    /**
     * 管理员查询首页模板列表
     */
    @PostMapping("/admin/index/templates")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public RspList<TdhIndexTemplateQueryRspBO> adminList(@RequestBody TdhIndexTemplateQueryReqBO request) {
        return tdhIndexTemplateApi.adminList(request);
    }

    /**
     * 新增首页模板（管理员）
     */
    @PostMapping("/admin/index/template/add")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp addTemplate(@RequestBody TdhIndexTemplateQueryReqBO reqBO) {
        return tdhIndexTemplateApi.addTemplate(reqBO);
    }

    /**
     * 更新首页模板（管理员）
     */
    @PostMapping("/admin/index/template/update")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp updateTemplate(@RequestBody TdhIndexTemplateQueryReqBO reqBO) {
        return tdhIndexTemplateApi.updateTemplate(reqBO);
    }

    /**
     * 删除首页模板（管理员）
     */
    @PostMapping("/admin/index/template/delete")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp deleteTemplate(@RequestBody TdhIndexTemplateDeleteReqBO request) {
        return tdhIndexTemplateApi.deleteTemplate(request);
    }

    /**
     * 首页模板排序（管理员）
     */
    @PostMapping("/admin/index/template/sort")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp sortTemplates(@RequestBody TdhIndexTemplateSortReqBO reqBO) {
        return tdhIndexTemplateApi.sortTemplates(reqBO);
    }

    /**
     * 首页模板上下架（管理员）
     */
    @PostMapping("/admin/index/template/status")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp updateTemplateStatus(@RequestBody TdhIndexTemplateStatusReqBO request) {
        return tdhIndexTemplateApi.updateTemplateStatus(request);
    }
}
