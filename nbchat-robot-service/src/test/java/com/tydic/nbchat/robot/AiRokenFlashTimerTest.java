package com.tydic.nbchat.robot;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.core.timer.AiRobotTokenFlashTimer;
import com.tydic.nbchat.robot.mapper.NbchatPresetPromptMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatPresetPrompt;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest
public class AiRokenFlashTimerTest {

    @Autowired
    private AiRobotTokenFlashTimer aiRobotTokenFlashTimer;
    @Autowired
    private NbchatPresetPromptMapper nbchatPresetPromptMapper;

    @Test
    public void testRunTimer() {
        aiRobotTokenFlashTimer.run();
    }

    @Test
    public void test() {
        NbchatPresetPrompt prompt = nbchatPresetPromptMapper.selectById("video_class");
        if (prompt != null) {
            JSONArray messages = new JSONArray();
            JSONObject message = new JSONObject();
            message.put("role", "user");
            message.put("content", prompt.getTemplate());
            messages.add(message);
            JSONObject body = new JSONObject();
            body.put("messages", messages);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("custom_id", "#taskId");
            jsonObject.put("body", body);
            log.info(jsonObject.toJSONString());
        }
    }
}
